package com.dell.it.hip.util;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class SFTPFileLockManager {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public boolean acquireLock(String integrationName, String version, String adapterId, String fileName, String ownerId, long ttlSeconds) {
        String key = HIPRedisKeyUtil.sftpFileLockKey(integrationName, version, adapterId, fileName);
        Boolean set = redisTemplate.opsForValue().setIfAbsent(key, ownerId, ttlSeconds, TimeUnit.SECONDS);
        return Boolean.TRUE.equals(set);
    }

    public void releaseLock(String integrationName, String version, String adapterId, String fileName) {
        String key = HIPRedisKeyUtil.sftpFileLockKey(integrationName, version, adapterId, fileName);
        redisTemplate.delete(key);
    }
}