package com.dell.it.hip.util.validation;

public class SchemaValidator {
    
    /**
     * Enumeration of supported schema formats for validation.
     * <p>
     * This enum defines the various schema formats that can be used
     * with the SchemaValidator to validate different types of payload data.
     * </p>
     */
    public enum SchemaFormat {
        /**
         * XML Schema Definition format for validating XML documents
         */
        XSD,
        
        /**
         * JSON Schema format for validating JSON documents
         */
        JSON_SCHEMA,
        
        /**
         * CSV Schema format for validating CSV files
         */
        CSV_SCHEMA,
        
        /**
         * EDI X12 format for validating Electronic Data Interchange documents
         */
        EDI_X12    }

    /**
     * Validates a payload against a schema.
     *
     * @param payload The payload data to validate
     * @param format A string representation of the payload format (e.g., "XML", "JSON").
     *              This parameter is reserved for future enhancements where automatic format detection
     *              and schema format selection might be implemented. Currently, the format detection 
     *              should be done using {@link MessageFormatDetector#detect(String)} and the appropriate 
     *              schemaFormat should be selected based on it.
     * @param schemaFormat The schema format to use for validation (required)
     * @param schemaLocation The location of the schema file
     * @return true if the payload is valid according to the schema, false otherwise
     * @throws Exception If an error occurs during validation
     */
public static boolean validate(String payload,
                               String format,
                               SchemaFormat schemaFormat,
                               String schemaLocation) throws Exception {
    if (schemaFormat == null) {
        throw new IllegalArgumentException("Schema format cannot be null");
    }
     switch (schemaFormat) {
         case XSD:
             return XmlSchemaValidator.validate(payload, schemaLocation);
            case JSON_SCHEMA:
                return JsonSchemaValidator.validate(payload, schemaLocation);
            case CSV_SCHEMA:
                return CsvSchemaValidator.validate(payload, schemaLocation);
            case EDI_X12:
                return EdiValidator.validateAgainstSchemaX12(payload, schemaLocation);
            default:
                return false;
        }
    }
}
