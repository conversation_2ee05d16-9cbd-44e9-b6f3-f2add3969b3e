package com.dell.it.hip.util.validation;

import java.io.StringReader;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.SchemaFactory;

public class XmlSchemaValidator {

    public static boolean isWellFormed(String xml) {
        try {
        	DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            builder.parse(new org.xml.sax.InputSource(new StringReader(xml)));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean validate(String xml, String xsdPath) throws Exception {
        SchemaFactory sf = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
        try (StringReader r = new StringReader(xml)) {
            sf.newSchema(new StreamSource(xsdPath))
              .newValidator()
              .validate(new StreamSource(r));
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
