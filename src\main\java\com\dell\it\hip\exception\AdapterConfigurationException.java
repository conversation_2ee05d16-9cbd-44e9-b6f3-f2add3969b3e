package com.dell.it.hip.exception;

/**
 * Exception thrown when there's an error in adapter configuration.
 */
public class AdapterConfigurationException extends RuntimeException {
    
    public AdapterConfigurationException(String message) {
        super(message);
    }
    
    public AdapterConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public AdapterConfigurationException(String adapterType, String configProperty, String reason) {
        super(String.format("Configuration error in %s adapter for property '%s': %s", 
                          adapterType, configProperty, reason));
    }
}
