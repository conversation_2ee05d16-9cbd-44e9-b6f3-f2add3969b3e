# Security Configuration 
app: 
  security: 
    # User authentication configuration - use environment variables in production 
    admin-username: ${ADMIN_USERNAME:admin} 
    admin-password: ${ADMIN_PASSWORD:admin} # In production, set via environment variables 
    standard-username: ${USER_USERNAME:user} 
    standard-password: ${USER_PASSWORD:user} # In production, set via environment variables 
    # CSRF configuration 
    csrf-enabled: true 
    csrf-ignore-urls: 
      - /api/v1/webhook/** 
      - /hip/management/register 
