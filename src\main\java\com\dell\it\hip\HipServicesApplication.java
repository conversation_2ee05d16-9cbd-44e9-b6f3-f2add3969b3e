package com.dell.it.hip;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Main Spring Boot application class for HIP Services.
 *
 * This application provides a dynamic, cluster-aware Spring Integration platform
 * with flow control, retry, tracing, and comprehensive monitoring capabilities.
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@IntegrationComponentScan
@EnableJpaRepositories(basePackages = {
    "com.dell.it.hip.core.repository",
    "com.dell.it.hip.core.registry",
    "com.dell.it.hip.security.repository",
    "com.dell.it.hip.util.routing"
})
public class HipServicesApplication {

    public static void main(String[] args) {
        SpringApplication.run(HipServicesApplication.class, args);
    }
}
