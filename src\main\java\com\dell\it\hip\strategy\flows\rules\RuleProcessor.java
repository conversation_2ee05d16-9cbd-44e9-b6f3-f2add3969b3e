package com.dell.it.hip.strategy.flows.rules;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.flowSteps.FlowStepConfig;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleActionDescriptor;
import com.dell.it.hip.config.rules.RuleCondition;
import com.dell.it.hip.core.registry.RuleActionRegistry;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;


@Component
public class RuleProcessor {

    @Autowired
    private RuleActionRegistry actionRegistry;

    @Autowired
    private WiretapService wiretapService;

    @Autowired
    private TransactionLoggingUtil txLogger;

    @Autowired
    private RuleCache ruleCache; // Service to fetch rules by name

    public Message<?> processRules(
            HIPIntegrationDefinition def,
            FlowStepConfig stepConfig,
            Message<?> originalMsg
    ) {
        List<String> ruleNames = stepConfig.getRules(); // Ordered list of rule names for this step
        Message<?> currentMsg = originalMsg;

        for (String ruleName : ruleNames) {
            // Use default version "1.0" for backward compatibility
            Rule rule = ruleCache.getRule(ruleName, "1.0");
            if (rule == null) {
                wiretapService.tap(currentMsg, def, stepConfig.getPropertyRef(), "warn",
                        "Rule not found: " + ruleName);
                continue;
            }
            if (!rule.isEnabled()) {
                wiretapService.tap(currentMsg, def, stepConfig.getPropertyRef(), "debug",
                        "Rule is disabled: " + ruleName);
                continue;
            }
            if (!documentTypeMatches(rule, currentMsg)) {
                wiretapService.tap(currentMsg, def, stepConfig.getPropertyRef(), "debug",
                        "Rule " + ruleName + " skipped: documentType mismatch");
                continue;
            }

            boolean matched = rule.isExecuteAlways() || evaluateConditions(rule, currentMsg);

            wiretapService.tap(currentMsg, def, stepConfig.getPropertyRef(), "info",
                    "Processing rule: " + ruleName + (matched ? " [MATCHED]" : " [SKIPPED]"));

            if (!matched) continue;

            for (RuleActionDescriptor actionDesc : rule.getActions()) {
                RuleAction action = actionRegistry.createAction(actionDesc);
                Message<?> result = action.performAction(currentMsg, actionDesc.getParams());
                txLogger.logRuleAction(def, stepConfig.getPropertyRef(), ruleName, actionDesc.getType(), result);

                // If StopRuleAction encountered, halt rule processing and return current message
                if ("StopRule".equalsIgnoreCase(actionDesc.getType())) {
                    wiretapService.tap(result, def, stepConfig.getPropertyRef(), "info",
                            "StopRuleAction encountered: rule execution stopped for this message.");
                    return result != null ? result : currentMsg;
                }

                // Update currentMsg with any mutation from action
                if (result != null) currentMsg = result;
            }
        }

        return currentMsg;
    }

    private boolean documentTypeMatches(Rule rule, Message<?> msg) {
        String ruleDocType = rule.getDocumentType();
        if (ruleDocType == null || ruleDocType.trim().isEmpty() || "any".equalsIgnoreCase(ruleDocType)) return true;
        Object headerVal = msg.getHeaders().get("HIP.documentType");
        if (headerVal == null) return false;
        return ruleDocType.equalsIgnoreCase(headerVal.toString());
    }

    private boolean evaluateConditions(Rule rule, Message<?> msg) {
        List<RuleCondition> conds = rule.getRuleConditions();
        if (conds == null || conds.isEmpty()) return true; // No conditions = always matches

        boolean andLogic = rule.getExecuteActionWhen() == rule.getExecuteActionWhen().ALL;

        boolean result = andLogic;
        for (RuleCondition cond : conds) {
            Object propValObj = msg.getHeaders().get(cond.getPropertyName());
            String propVal = propValObj != null ? propValObj.toString() : null;

                boolean match = switch (cond.getOperator()) {
                case EQUALS -> propVal != null && propVal.equals(cond.getValue());
                case STARTS_WITH -> propVal != null && propVal.startsWith(cond.getValue());
                case CONTAINS -> propVal != null && propVal.contains(cond.getValue());
            };

            if (!andLogic && match) return true;
            if (andLogic && !match) return false;
        }
        return andLogic;
    }

    // Enum inline for self-contained code
    public enum Operator {
        EQUALS, STARTS_WITH, CONTAINS
    }
}