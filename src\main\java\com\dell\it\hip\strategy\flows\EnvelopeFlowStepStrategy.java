package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.flowSteps.EnvelopeFlowStepConfig;
import com.dell.it.hip.config.flowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.dell.it.hip.util.validation.StructuralValidator;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("envelope")
public class EnvelopeFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired
    private TransactionLoggingUtil transactionLoggingUtil;
    @Autowired
    private WiretapService wiretapService;

    @Override
    public String getType() {
        return "envelope";
    }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        EnvelopeFlowStepConfig config = (EnvelopeFlowStepConfig) def.getConfigMap().get(stepRef.getPropertyRef());
        if (config == null) throw new IllegalStateException("No envelope config for: " + stepRef.getPropertyRef());

        String payload = Objects.toString(message.getPayload(), "");
        String dataFormat = null;

        // 1. Try to pick dataFormat from message header, else from config, else detect
        if (message.getHeaders().containsKey("HIP.payload.dataformat")) {
            dataFormat = String.valueOf(message.getHeaders().get("HIP.payload.dataformat"));
        }
        if (dataFormat == null && config.getDataFormat() != null) {
            dataFormat = config.getDataFormat();
        }
        if (dataFormat == null) {
            dataFormat = MessageFormatDetector.detect(payload);
        }

        // 2. Envelope the payload
        String envelopeHeader = config.getEnvelopeHeader();
        String envelopeFooter = config.getEnvelopeFooter();
        String separator = config.getSeparator();

        // Handle dynamic headers if present (e.g., placeholders to be replaced)
        if (config.getDynamicHeaders() != null && !config.getDynamicHeaders().isEmpty() && envelopeHeader != null) {
            for (Map.Entry<String, String> entry : config.getDynamicHeaders().entrySet()) {
                envelopeHeader = envelopeHeader.replace("{{" + entry.getKey() + "}}", entry.getValue());
            }
        }
        // Default separator by format if not set
        if (separator == null) {
            separator = switch (dataFormat.toUpperCase()) {
                case "EDI_X12" -> "~";
                case "EDI_EDIFACT" -> "'";
                case "CSV" -> "\n";
                case "XML", "JSON" -> "";
                default -> "";
            };
        }

        String envelopedPayload;
        if (envelopeHeader != null && envelopeFooter != null) {
            envelopedPayload = envelopeHeader + separator + payload + separator + envelopeFooter;
        } else if (envelopeHeader != null) {
            envelopedPayload = envelopeHeader + separator + payload;
        } else if (envelopeFooter != null) {
            envelopedPayload = payload + separator + envelopeFooter;
        } else {
            envelopedPayload = payload;
        }

        // 3. Structural validation (if enabled)
        boolean valid = true;
        if (config.isValidateEnvelope()) {
            valid = StructuralValidator.validate(envelopedPayload, dataFormat);
        }

        // 4. Build new message with envelope and updated header
        MessageBuilder<String> builder = MessageBuilder
                .withPayload(envelopedPayload)
                .copyHeaders(message.getHeaders())
                .setHeader("HIP.payload.dataformat", dataFormat);

        Message<?> resultMsg = builder.build();

        if (!valid) {
            String err = "Envelope structural validation failed for dataFormat: " + dataFormat;
            log.warn(err);
            wiretapService.tap(resultMsg, def, stepRef, "error", err);
            transactionLoggingUtil.logError(resultMsg, def, stepRef,"EnvelopeFlowStep", err);
            // Optionally, halt message flow here or allow pass-through:
            return Collections.emptyList();
        } else {
            wiretapService.tap(resultMsg, def, stepRef, "info", "Envelope added and validated for dataFormat: " + dataFormat);
            transactionLoggingUtil.logInfo(resultMsg, def, stepRef, "Envelope added and validated for dataFormat: " + dataFormat);
            return Collections.singletonList(resultMsg);
        }
    }
}
