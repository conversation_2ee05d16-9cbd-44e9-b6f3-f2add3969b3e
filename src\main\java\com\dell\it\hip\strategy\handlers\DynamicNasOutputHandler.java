package com.dell.it.hip.strategy.handlers;

import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicNasHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.WiretapService;

import jcifs.CIFSContext;
import jcifs.config.PropertyConfiguration;
import jcifs.context.BaseContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbFile;

@Component("dynamicNasOutputHandler")
public class DynamicNasOutputHandler extends AbstractOutputHandlerStrategy {

    private final Set<String> headersToFilter;
    private final Map<String, AtomicBoolean> handlerPauseState = new ConcurrentHashMap<>();

    public DynamicNasOutputHandler(
            OpenTelemetryPropagationUtil otelUtil,
            WiretapService wiretapService,
            ArchiveService archiveService,
            RetryTemplateFactory retryTemplateFactory,
            Set<String> headersToFilter
    ) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
        this.headersToFilter = headersToFilter;
    }

    @Override
    public String getType() {
        return "nas";
    }

    @Override
    protected void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        if (isPaused(def, ref)) {
            throw new IllegalStateException("Handler [" + ref.getType() + "] is paused. Message not delivered.");
        }

        DynamicNasHandlerConfig config = def.getConfig(ref.getId(), DynamicNasHandlerConfig.class);
        if (config == null)
            throw new IllegalArgumentException("DynamicNasHandlerConfig not found for ref: " + ref.getId());

        String protocol = config.getProtocol();
        String fileName = (String) message.getHeaders().get("HIP.output.FileName");
        if (fileName == null || fileName.trim().isEmpty()) {
            wiretapService.tap(message, def, ref, "error", "Missing HIP.output.FileName header; cannot write NAS file.");
            throw new IllegalArgumentException("Missing HIP.output.FileName header; cannot write NAS file.");
        }
        byte[] payload = Boolean.TRUE.equals(config.getGzipEnabled())
                ? CompressionUtil.compress(message.getPayload())
                : convertToBytes(message.getPayload());

        if ("smb".equalsIgnoreCase(protocol)) {
            handleSmbWrite(config, fileName, payload, message, def, ref);
        } else if ("nfs".equalsIgnoreCase(protocol)) {
            handleNfsWrite(config, fileName, payload, message, def, ref);
        } else {
            wiretapService.tap(message, def, ref, "error", "Unsupported NAS protocol: " + protocol);
            throw new IllegalArgumentException("Unsupported NAS protocol: " + protocol);
        }
    }

    private void handleSmbWrite(DynamicNasHandlerConfig config, String fileName, byte[] payload,
                                Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        // JCIFS-NG context setup
        Properties prop = new Properties();
        prop.setProperty("jcifs.smb.client.enableSMB2", "true");
        CIFSContext base = new BaseContext(new PropertyConfiguration(prop));
        CIFSContext context = base.withCredentials(
                new NtlmPasswordAuthenticator(
                        config.getDomain() != null ? config.getDomain() : "",
                        config.getUsername(),
                        config.getPassword() != null ? config.getPassword() : ""
                )
        );
        // smb://host/share/dir/file
        String smbPath = "smb://" + config.getHost() + "/" + config.getShareName() + "/" +
                (config.getRemoteDirectory() != null && !config.getRemoteDirectory().isEmpty() ?
                        config.getRemoteDirectory().replace("\\", "/").replaceAll("^/+", "").replaceAll("/+$", "") + "/" : "")
                + fileName;
        SmbFile file = new SmbFile(smbPath, context);

        if (file.exists()) {
            wiretapService.tap(message, def, ref, "error", "SMB file already exists: " + smbPath);
            throw new RuntimeException("SMB file already exists: " + smbPath);
        }
        // Write file
        try (OutputStream os = file.getOutputStream()) {
            os.write(payload);
        }
        wiretapService.tap(message, def, ref, "completed", "SMB upload successful: " + smbPath);
    }

    private void handleNfsWrite(DynamicNasHandlerConfig config, String fileName, byte[] payload,
                                Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        String dir = config.getRemoteDirectory() != null ? config.getRemoteDirectory() : ".";
        String nfsFilePath = dir.endsWith(config.getFileSeparator()) ? dir + fileName : dir + config.getFileSeparator() + fileName;
        Path filePath = Paths.get(nfsFilePath);

        if (Files.exists(filePath)) {
            wiretapService.tap(message, def, ref, "error", "NFS file already exists: " + nfsFilePath);
            throw new RuntimeException("NFS file already exists: " + nfsFilePath);
        }
        Files.write(filePath, payload, StandardOpenOption.CREATE_NEW, StandardOpenOption.WRITE);
        wiretapService.tap(message, def, ref, "completed", "NFS upload successful: " + nfsFilePath);
    }

    private byte[] convertToBytes(Object payload) {
        if (payload == null) return new byte[0];
        if (payload instanceof byte[] bytes) return bytes;
        if (payload instanceof String str) return str.getBytes(StandardCharsets.UTF_8);
        return String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
    }

    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return handlerPauseState.getOrDefault(ref.getId(), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.remove(ref.getId());
        super.shutdown(def, ref);
    }

    @Override
    public void dispose() {
        handlerPauseState.clear();
        super.dispose();
    }
}