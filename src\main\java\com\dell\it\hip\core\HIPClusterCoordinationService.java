package com.dell.it.hip.core;

import java.time.Duration;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.HIPRedisKeyUtil;
import com.dell.it.hip.util.ThrottleSettings;
@Service
public class HIPClusterCoordinationService {

    private final String serviceManagerName;
    private final StringRedisTemplate redisTemplate;
    private final RedisMessageListenerContainer redisListenerContainer;

    // Topics for cluster events (all tenant-safe)
    private final ChannelTopic refillTopic;
    private final ChannelTopic throttleChangedTopic;
    private final ChannelTopic pauseChangedTopic;
    private final ChannelTopic registrationTopic;
    private final ChannelTopic unregistrationTopic;
    private final ChannelTopic customEventTopic;

    public HIPClusterCoordinationService(
            @Value("${service.manager.name}") String serviceManagerName,
            StringRedisTemplate redisTemplate,
            RedisMessageListenerContainer redisListenerContainer) {
        this.serviceManagerName = serviceManagerName;
        this.redisTemplate = redisTemplate;
        this.redisListenerContainer = redisListenerContainer;
        this.refillTopic = new ChannelTopic(HIPRedisKeyUtil.clusterRefillTopic(serviceManagerName));
        this.throttleChangedTopic = new ChannelTopic(HIPRedisKeyUtil.clusterThrottleChangedTopic(serviceManagerName));
        this.pauseChangedTopic = new ChannelTopic(HIPRedisKeyUtil.clusterPauseChangedTopic(serviceManagerName));
        this.registrationTopic = new ChannelTopic(HIPRedisKeyUtil.clusterRegistrationTopic(serviceManagerName));
        this.unregistrationTopic = new ChannelTopic(HIPRedisKeyUtil.clusterUnregistrationTopic(serviceManagerName));
        this.customEventTopic = new ChannelTopic(HIPRedisKeyUtil.clusterEventTopic(serviceManagerName));
    }

    // ========== PAUSE/RESUME - single instance ==========

    public void pause(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.adapterPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        redisTemplate.opsForValue().set(key, "PAUSED");
        broadcastPauseChanged(def, ref, true);
    }

    public void resume(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.adapterPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        redisTemplate.delete(key);
        broadcastPauseChanged(def, ref, false);
    }

    // ========== PAUSE/RESUME - all adapters for a definition ==========

    public void pauseAll(HIPIntegrationDefinition def) {
        def.getAdapterConfigRefs().forEach(ref -> pause(def, ref));
    }

    public void resumeAll(HIPIntegrationDefinition def) {
        def.getAdapterConfigRefs().forEach(ref -> resume(def, ref));
    }

    // ========== SHUTDOWN ==========

    public void shutdown(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        removeThrottle(def, ref);
        resume(def, ref); // ensure not paused
        // Optionally broadcast shutdown as custom event
        broadcastCustomEvent("SHUTDOWN:" + def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getId());
    }

    public void shutdownAll(HIPIntegrationDefinition def) {
        def.getAdapterConfigRefs().forEach(ref -> shutdown(def, ref));
    }

    // ========== THROTTLE - per instance ==========

    public void setThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings settings) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        if (settings == null) {
            redisTemplate.delete(key);
        } else {
            redisTemplate.opsForValue().set(key, settings.toString());
        }
        broadcastThrottleUpdate(def, ref, "THROTTLE_UPDATED");
    }

    public void removeThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        redisTemplate.delete(key);
        broadcastThrottleUpdate(def, ref, "THROTTLE_REMOVED");
    }

    // ========== THROTTLE - all adapters for a definition ==========

    public void setThrottleAll(HIPIntegrationDefinition def, ThrottleSettings settings) {
        def.getAdapterConfigRefs().forEach(ref -> setThrottle(def, ref, settings));
    }

    public void removeThrottleAll(HIPIntegrationDefinition def) {
        def.getAdapterConfigRefs().forEach(ref -> removeThrottle(def, ref));
    }

    // ========== RATE LIMIT CHECK ==========

    public boolean isInputAllowed(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings settings) {
        if (settings == null || settings.getMaxMessagesPerPeriod() <= 0 || settings.getPeriodSeconds() <= 0) {
            return true;
        }
        String key = HIPRedisKeyUtil.rateLimitKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        Long count = redisTemplate.opsForValue().increment(key);
        if (count != null && count == 1L) {
            redisTemplate.expire(key, Duration.ofSeconds(settings.getPeriodSeconds()));
        }
        boolean allowed = count != null && count <= settings.getMaxMessagesPerPeriod();
        if (!allowed) {
            broadcastThrottleUpdate(def, ref, "THROTTLED");
        }
        return allowed;
    }

    // ========== DEDUPLICATION ==========

    public boolean isDuplicate(HIPIntegrationDefinition def, AdapterConfigRef ref, String dedupId, long ttlSeconds) {
        String key = HIPRedisKeyUtil.dedupKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId()) + ":" + dedupId;
        Boolean alreadySeen = redisTemplate.hasKey(key);
        if (alreadySeen == null || !alreadySeen) {
            redisTemplate.opsForValue().set(key, "1", Duration.ofSeconds(ttlSeconds));
            return false;
        }
        return true;
    }

    // ========== REGISTRATION/UNREGISTRATION EVENTS ==========

    public void broadcastRegistration(HIPIntegrationDefinition def) {
        String msg = def.getHipIntegrationName() + ":" + def.getVersion() + ":REGISTERED";
        redisTemplate.convertAndSend(registrationTopic.getTopic(), msg);
    }

    public void broadcastUnregistration(HIPIntegrationDefinition def) {
        String msg = def.getHipIntegrationName() + ":" + def.getVersion() + ":UNREGISTERED";
        redisTemplate.convertAndSend(unregistrationTopic.getTopic(), msg);
    }

    // ========== PAUSE/RESUME EVENT BROADCAST ==========

    public void broadcastPauseChanged(HIPIntegrationDefinition def, AdapterConfigRef ref, boolean paused) {
        String msg = def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getId() + (paused ? ":PAUSED" : ":RESUMED");
        redisTemplate.convertAndSend(pauseChangedTopic.getTopic(), msg);
    }

    // ========== THROTTLE EVENT BROADCAST ==========

    public void broadcastThrottleUpdate(HIPIntegrationDefinition def, AdapterConfigRef ref, String state) {
        String msg = def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getId() + ":" + state;
        redisTemplate.convertAndSend(throttleChangedTopic.getTopic(), msg);
    }
    public void broadcastSftpForcePoll(String integrationName, String version, String adapterId) {
        redisTemplate.convertAndSend(
                HIPRedisKeyUtil.clusterSftpForcePollTopic(integrationName, version, adapterId),
                "FORCE_POLL:" + integrationName + ":" + version + ":" + adapterId
        );
    }
    // ========== REFILL AND CUSTOM EVENTS ==========

    public void broadcastRefill(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String msg = def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getId() + ":REFILL";
        redisTemplate.convertAndSend(refillTopic.getTopic(), msg);
    }

    public void broadcastCustomEvent(String payload) {
        redisTemplate.convertAndSend(customEventTopic.getTopic(), payload);
    }

    public void publishEvent(ChannelTopic topic, String message) {
        redisTemplate.convertAndSend(topic.getTopic(), message);
    }

    // ========== EVENT LISTENER REGISTRATION ==========

    public void registerPauseChangedListener(Consumer<String> callback) {
        registerListener(pauseChangedTopic, callback);
    }

    public void registerThrottleUpdateListener(Consumer<String> callback) {
        registerListener(throttleChangedTopic, callback);
    }

    public void registerRegistrationListener(Consumer<String> callback) {
        registerListener(registrationTopic, callback);
    }

    public void registerUnregistrationListener(Consumer<String> callback) {
        registerListener(unregistrationTopic, callback);
    }

    public void registerCustomEventListener(Consumer<String> callback) {
        registerListener(customEventTopic, callback);
    }

    public void registerListener(ChannelTopic topic, Consumer<String> callback) {
        redisListenerContainer.addMessageListener(
                (MessageListener) (message, pattern) -> {
                    String msg = new String(message.getBody());
                    callback.accept(msg);
                },
                topic
        );
    }

    // ========== STATE QUERIES ==========

    public boolean isAdapterPaused(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.adapterPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        return redisTemplate.hasKey(key);
    }

    public boolean isAdapterThrottled(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        return redisTemplate.hasKey(key);
    }

    // ========== UTILITY ==========

    public String integrationAdapterKey(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        return def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getId();
    }
}