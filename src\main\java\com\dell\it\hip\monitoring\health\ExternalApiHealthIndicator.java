package com.dell.it.hip.monitoring.health;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * Health indicator for external API availability.
 */
@Component
public class ExternalApiHealthIndicator implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(ExternalApiHealthIndicator.class);

    @Value("${hip.health.external-apis:}")
    private List<String> externalApiUrls;

    private final RestTemplate restTemplate;

    public ExternalApiHealthIndicator() {
        this.restTemplate = new RestTemplate();
        // Set timeout for health checks
        this.restTemplate.getRequestFactory();
    }

    @Override
    public Health health() {
        Map<String, Object> details = new HashMap<>();
        boolean allHealthy = true;

        if (externalApiUrls == null || externalApiUrls.isEmpty()) {
            return Health.up()
                    .withDetail("external-apis", "No external APIs configured for health check")
                    .build();
        }

        for (String apiUrl : externalApiUrls) {
            try {
                Instant start = Instant.now();
                ResponseEntity<String> response = restTemplate.exchange(
                        apiUrl, HttpMethod.GET, null, String.class);
                Duration responseTime = Duration.between(start, Instant.now());

                if (response.getStatusCode().is2xxSuccessful()) {
                    details.put(apiUrl, Map.of(
                            "status", "UP",
                            "responseTime", responseTime.toMillis() + "ms",
                            "httpStatus", response.getStatusCode().value()
                    ));
                } else {
                    allHealthy = false;
                    details.put(apiUrl, Map.of(
                            "status", "DOWN",
                            "responseTime", responseTime.toMillis() + "ms",
                            "httpStatus", response.getStatusCode().value(),
                            "reason", "Non-2xx response"
                    ));
                }
            } catch (Exception ex) {
                allHealthy = false;
                details.put(apiUrl, Map.of(
                        "status", "DOWN",
                        "error", ex.getMessage(),
                        "reason", "Connection failed"
                ));
                logger.warn("Health check failed for external API: {}", apiUrl, ex);
            }
        }

        Health.Builder healthBuilder = allHealthy ? Health.up() : Health.down();
        return healthBuilder
                .withDetail("external-apis", details)
                .build();
    }
}
