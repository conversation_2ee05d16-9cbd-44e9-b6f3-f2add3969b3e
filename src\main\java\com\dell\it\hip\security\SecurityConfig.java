package com.dell.it.hip.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Security configuration for the application
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Autowired
    private SecurityProperties securityProperties;
    
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            // CSRF disabled for stateless API with token-based authentication
 // CSRF disabled for stateless API with token-based authentication
 .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authorize -> authorize
                // Allow Swagger UI and API docs
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
 // Allow only specific actuator endpoints or require admin role
 .requestMatchers("/actuator/health", "/actuator/info").permitAll()
 .requestMatchers("/actuator/**").hasRole("ADMIN")
                // Require authentication for all other endpoints
                .anyRequest().authenticated()
            )
            .httpBasic()
            .and()
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS);
        
        return http.build();
    }

    @Bean
    public UserDetailsService userDetailsService() {
        // Load user credentials from external configuration
        UserDetails admin = User.builder()
                .username(securityProperties.getAdminUsername())
                .password(passwordEncoder().encode(securityProperties.getAdminPassword()))
                .roles("ADMIN")
                .build();
        
        UserDetails user = User.builder()
                .username(securityProperties.getStandardUsername())
                .password(passwordEncoder().encode(securityProperties.getStandardPassword()))
                .roles("USER")
                .build();
        
        return new InMemoryUserDetailsManager(admin, user);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
