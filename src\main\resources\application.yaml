server:
  port: 8080

spring:
  main:
    allow-circular-references: true
  datasource:
    url: ***************************************************************************************************************************************************************************************="CN=oracledb.dev.amer.dell.com,O=Dell Technologies Inc.,L=Round Rock,ST=Texas,C=US")))
    username: SHARED
    password: aicdevpassword321_
    driver-class-name: oracle.jdbc.OracleDriver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
  data:
    redis:
      host: hip-gscm-redis-dev.rds-a2-np.kob.dell.com
      port: 443
      password: sFjIvYHMJHcv
      embedded:
        enabled: false
  config:
    import: classpath:security-config.yaml
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false

service:
  manager:
    name: OrderIntegrationManager
  concurrency:
    corePoolSize: 12
    maxPoolSize: 48
    queueCapacity: 1000

hip:
  kafka:
    bootstrap-servers: "kafka1:9092,kafka2:9092"
    security-protocol: SASL_PLAINTEXT
    sasl-mechanism: SCRAM-SHA-256

# Logging and monitoring configs...
logging:
  level:
    root: info

