package com.dell.it.hip.integration;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import org.junit.Ignore;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import com.dell.it.hip.util.HIPRedisKeyUtil;

/**
 * Integration tests for Redis functionality using TestContainers.
 * Extends BaseIntegrationTest for shared container setup and Docker validation.
 */
@Disabled
class RedisIntegrationTest extends BaseIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisIntegrationTest.class);

    @Autowired
    private StringRedisTemplate redisTemplate;

    @BeforeEach
    void setUp() {
        // Clear Redis before each test
        try {
            redisTemplate.getConnectionFactory().getConnection().serverCommands().flushAll();
            logger.debug("Redis cleared successfully before test");
        } catch (Exception e) {
            logger.warn("Failed to clear Redis before test: {}", e.getMessage());
        }
    }

    @Test    
    void testRedisConnection() {
        // Test basic Redis connectivity
        String key = "test:connection";
        String value = "connected";

        redisTemplate.opsForValue().set(key, value);
        String retrieved = redisTemplate.opsForValue().get(key);

        assertEquals(value, retrieved);
    }

    @Test
    void testThrottleKeyOperations() {
        // Test throttle key operations
        String throttleKey = HIPRedisKeyUtil.throttleKey("test-service", "test-integration", "1.0", "adapter-1");

        // Set initial count
        redisTemplate.opsForValue().set(throttleKey, "5");

        // Increment count
        Long newCount = redisTemplate.opsForValue().increment(throttleKey);
        assertEquals(6L, newCount);

        // Set expiration
        redisTemplate.expire(throttleKey, Duration.ofMinutes(1));
        Long ttl = redisTemplate.getExpire(throttleKey, TimeUnit.SECONDS);
        assertTrue(ttl > 0 && ttl <= 60);
    }

    @Test
    void testPubSubOperations() {
        // Test pub/sub operations
        String topic = HIPRedisKeyUtil.clusterRefillTopic("test-service", "test-integration", "1.0");
        String message = "refill-event";

        // This is a basic test - in a real scenario you'd set up listeners
        redisTemplate.convertAndSend(topic, message);

        // Verify the operation doesn't throw exceptions
        assertDoesNotThrow(() -> {
            redisTemplate.convertAndSend(topic, message);
        });
    }

    @Test
    void testMultipleKeyOperations() {
        // Test operations with multiple keys
        String key1 = HIPRedisKeyUtil.throttleKey("service1", "integration1", "1.0");
        String key2 = HIPRedisKeyUtil.throttleKey("service2", "integration2", "2.0");

        redisTemplate.opsForValue().set(key1, "10");
        redisTemplate.opsForValue().set(key2, "20");

        assertEquals("10", redisTemplate.opsForValue().get(key1));
        assertEquals("20", redisTemplate.opsForValue().get(key2));

        // Test batch operations
        redisTemplate.delete(key1);
        assertNull(redisTemplate.opsForValue().get(key1));
        assertEquals("20", redisTemplate.opsForValue().get(key2));
    }

    @Test
    void testKeyExpiration() {
        // Test key expiration functionality
        String key = HIPRedisKeyUtil.throttleKey("test-service", "test-integration", "1.0");

        redisTemplate.opsForValue().set(key, "test-value", Duration.ofSeconds(2));

        // Verify key exists
        assertTrue(redisTemplate.hasKey(key));

        // Wait for expiration (in real tests, you might use awaitility)
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Verify key has expired
        assertFalse(redisTemplate.hasKey(key));
    }

    @Test
    void testAtomicOperations() {
        // Test atomic operations for throttling
        String key = HIPRedisKeyUtil.throttleKey("test-service", "test-integration", "1.0");

        // Test increment from non-existent key
        Long count1 = redisTemplate.opsForValue().increment(key);
        assertEquals(1L, count1);

        // Test increment from existing key
        Long count2 = redisTemplate.opsForValue().increment(key);
        assertEquals(2L, count2);

        // Test increment by specific amount
        Long count3 = redisTemplate.opsForValue().increment(key, 5);
        assertEquals(7L, count3);
    }

    @Test
    void testHashOperations() {
        // Test hash operations for complex data structures
        String hashKey = "hip:test-service:config:test-integration:1.0";

        redisTemplate.opsForHash().put(hashKey, "maxRequests", "100");
        redisTemplate.opsForHash().put(hashKey, "timeWindow", "60");
        redisTemplate.opsForHash().put(hashKey, "enabled", "true");

        assertEquals("100", redisTemplate.opsForHash().get(hashKey, "maxRequests"));
        assertEquals("60", redisTemplate.opsForHash().get(hashKey, "timeWindow"));
        assertEquals("true", redisTemplate.opsForHash().get(hashKey, "enabled"));

        // Test hash size
        assertEquals(3L, redisTemplate.opsForHash().size(hashKey));
    }

    @Test
    void testListOperations() {
        // Test list operations for message queues
        String listKey = "hip:test-service:queue:test-integration:1.0";

        // Push messages to queue
        redisTemplate.opsForList().leftPush(listKey, "message1");
        redisTemplate.opsForList().leftPush(listKey, "message2");
        redisTemplate.opsForList().leftPush(listKey, "message3");

        // Check queue size
        assertEquals(3L, redisTemplate.opsForList().size(listKey));

        // Pop messages from queue
        assertEquals("message3", redisTemplate.opsForList().rightPop(listKey));
        assertEquals("message2", redisTemplate.opsForList().rightPop(listKey));
        assertEquals("message1", redisTemplate.opsForList().rightPop(listKey));

        // Verify queue is empty
        assertEquals(0L, redisTemplate.opsForList().size(listKey));
    }

    @Test
    void testSetOperations() {
        // Test set operations for unique collections
        String setKey = "hip:test-service:active-integrations";

        redisTemplate.opsForSet().add(setKey, "integration1:1.0");
        redisTemplate.opsForSet().add(setKey, "integration2:1.0");
        redisTemplate.opsForSet().add(setKey, "integration1:1.0"); // Duplicate

        // Verify set size (duplicates not counted)
        assertEquals(2L, redisTemplate.opsForSet().size(setKey));

        // Test membership
        assertTrue(redisTemplate.opsForSet().isMember(setKey, "integration1:1.0"));
        assertFalse(redisTemplate.opsForSet().isMember(setKey, "integration3:1.0"));

        // Remove member
        redisTemplate.opsForSet().remove(setKey, "integration1:1.0");
        assertEquals(1L, redisTemplate.opsForSet().size(setKey));
    }
}
