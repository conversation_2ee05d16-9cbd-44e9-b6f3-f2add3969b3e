configserver_uri=https://configserveruser:<EMAIL>
configproperties_sheet_name=hip-services

# Disable Spring Cloud Config
spring.cloud.config.enabled=false
spring.cloud.config.import-check.enabled=false

# Redis configuration
spring.data.redis.host=localhost
spring.data.redis.port=6370
spring.data.redis.embedded.enabled=true

# Service configuration
service.manager.name=OrderIntegrationManager
service.concurrency.corePoolSize=16
service.concurrency.maxPoolSize=48
service.concurrency.queueCapacity=1000

# HIP configuration
hip.kafka.bootstrap-servers=kafka1:9092,kafka2:9092
hip.kafka.security-protocol=SASL_PLAINTEXT

management.endpoints.web.exposure.include=*
management.endpoint.prometheus.enabled=true
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true