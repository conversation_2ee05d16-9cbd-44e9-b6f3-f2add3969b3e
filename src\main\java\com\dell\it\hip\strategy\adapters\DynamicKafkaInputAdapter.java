package com. dell. it. hip. strategy. adapters;
import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.AbstractMessageListenerContainer;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.MessageListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.logging.WiretapService;


@Component("kafka")
public class DynamicKafkaInputAdapter extends AbstractDynamicInputAdapter {

    private final Logger logger = LoggerFactory.getLogger(DynamicKafkaInputAdapter.class);

    @Autowired
    private Map<String, MessageChannel> inputChannels; // inputChannelName -> MessageChannel

    @Autowired
    private ConsumerFactory<Object, Object> consumerFactory;

    @Autowired
    private WiretapService wiretapService;

    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;

    @Override
    public String getType() {
        return "kafka";
    }

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        DynamicKafkaAdapterConfig cfg = (DynamicKafkaAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null) {
            throw new IllegalStateException("No config found for Kafka adapter ref: " + ref.getPropertyRef());
        }

        Map<String, Object> consumerProps = buildConsumerProperties(cfg);
        DefaultKafkaConsumerFactory<Object, Object> localConsumerFactory = new DefaultKafkaConsumerFactory<>(consumerProps);

        ContainerProperties containerProps = new ContainerProperties(cfg.getTopic());
        containerProps.setGroupId(cfg.getGroupId());
        containerProps.setPollTimeout(cfg.getPollTimeoutMs() != null ? cfg.getPollTimeoutMs() : 3000L);

        MessageChannel inputChannel = getInputChannel(def);

        ConcurrentMessageListenerContainer<Object, Object> container =
                new ConcurrentMessageListenerContainer<>(localConsumerFactory, containerProps);

        if (cfg.getConcurrency() != null && cfg.getConcurrency() > 1) {
            container.setConcurrency(cfg.getConcurrency());
        }

        container.setupMessageListener((MessageListener<Object, Object>) record -> {
            Object value = record.value();
            // Handle decompression
            if (cfg.isCompressed() && value instanceof byte[] bytes) {
                value = CompressionUtil.decompress(bytes);
            }

            // Convert to Spring Message
            Message<?> msg = toMessage(def, ref, record, value);

            // Propagate OTel context
            openTelemetryPropagationUtil.propagate(msg);

            // Wiretap for receive event
            wiretapService.tap(msg, def, ref, "received", "Message received from Kafka topic: " + record.topic());

            // Forward to input channel (i.e., enter HIP flow)
            processInboundMessage(def, ref, msg, inputChannel);
        });

        KafkaAdapterInstance instance = new KafkaAdapterInstance(container, inputChannel, cfg.getTopic(), cfg.getGroupId(), ref);
        registerAdapterInstance(def, ref, instance);

        container.start();
        logger.info("Kafka listener started for topic={}, groupId={}, ref={}", cfg.getTopic(), cfg.getGroupId(), ref.getId());
    }

    private Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, ConsumerRecord<?, ?> record, Object payload) {
        MessageBuilder<Object> mb = MessageBuilder.withPayload(payload);

        // Extract headers from Kafka record as Map<String, Object>
        Map<String, Object> kafkaHeadersMap = new HashMap<>();
        record.headers().forEach(header -> {
            if (header.value() != null) {
                kafkaHeadersMap.put(header.key(), new String(header.value()));
            }
        });

        // Add Kafka metadata as headers
        mb.setHeader("kafka_topic", record.topic());
        mb.setHeader("kafka_partition", record.partition());
        mb.setHeader("kafka_offset", record.offset());
        // Promote headers: generic/canonical are top-level, all others are grouped
        promoteHeaders(kafkaHeadersMap, mb, def, ref);

        return mb.build();
    }

    @Override
    protected Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        if (raw instanceof ConsumerRecord<?, ?> record) {
            Object payload = record.value();
            return toMessage(def, ref, record, payload);
        }
        // fallback
        return MessageBuilder.withPayload(raw).build();
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof KafkaAdapterInstance kafkaInst) {
            kafkaInst.container.stop();
            logger.info("Kafka container stopped for ref={}", ref.getId());
        }
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) {
        if (instance instanceof KafkaAdapterInstance kafkaInst) {
            kafkaInst.container.start();
            logger.info("Kafka container started for ref={}", kafkaInst.ref.getId());
        }
    }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) {
        if (instance instanceof KafkaAdapterInstance kafkaInst) {
            kafkaInst.container.stop();
            logger.info("Kafka container stopped for ref={}", kafkaInst.ref.getId());
        }
    }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof KafkaAdapterInstance kafkaInst) {
            AbstractMessageListenerContainer<?, ?> container = kafkaInst.container;
            if (container.isRunning()) {
                container.pause();
                logger.info("Kafka consumer paused for ref={}", ref.getId());
            }
        }
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof KafkaAdapterInstance kafkaInst) {
            AbstractMessageListenerContainer<?, ?> container = kafkaInst.container;
            if (container.isContainerPaused()) {
                container.resume();
                logger.info("Kafka consumer resumed for ref={}", ref.getId());
            }
        }
    }

    // --- AdapterInstance concrete class for Kafka ---
    public static class KafkaAdapterInstance extends AdapterInstance {
        final AbstractMessageListenerContainer<Object, Object> container;
        final MessageChannel inputChannel;
        final String topic;
        final String groupId;
        final AdapterConfigRef ref;

        public KafkaAdapterInstance(
                AbstractMessageListenerContainer<Object, Object> container,
                MessageChannel inputChannel,
                String topic,
                String groupId,
                AdapterConfigRef ref
        ) {
            this.container = container;
            this.inputChannel = inputChannel;
            this.topic = topic;
            this.groupId = groupId;
            this.ref = ref;
        }
    }

    protected Map<String, Object> buildConsumerProperties(DynamicKafkaAdapterConfig cfg) {
        Map<String, Object> props = new HashMap<>();

        // Required
        props.put("bootstrap.servers", cfg.getBootstrapServers());
        props.put("group.id", cfg.getGroupId());

        // Optional performance/reliability
        if (cfg.getAutoOffsetReset() != null) props.put("auto.offset.reset", cfg.getAutoOffsetReset());
        if (cfg.getMaxPollRecords() != null) props.put("max.poll.records", cfg.getMaxPollRecords());
        if (cfg.getFetchMinBytes() != null) props.put("fetch.min.bytes", cfg.getFetchMinBytes());
        if (cfg.getFetchMaxBytes() != null) props.put("fetch.max.bytes", cfg.getFetchMaxBytes());
        if (cfg.getMaxPartitionFetchBytes() != null) props.put("max.partition.fetch.bytes", cfg.getMaxPartitionFetchBytes());
        if (cfg.getSessionTimeoutMs() != null) props.put("session.timeout.ms", cfg.getSessionTimeoutMs());
        if (cfg.getHeartbeatIntervalMs() != null) props.put("heartbeat.interval.ms", cfg.getHeartbeatIntervalMs());
        if (cfg.getPollTimeoutMs() != null) props.put("poll.timeout.ms", cfg.getPollTimeoutMs());
        if (cfg.getEnableAutoCommit() != null) props.put("enable.auto.commit", cfg.getEnableAutoCommit());
        if (cfg.getAutoCommitIntervalMs() != null) props.put("auto.commit.interval.ms", cfg.getAutoCommitIntervalMs());
        if (cfg.getMaxPollIntervalMs() != null) props.put("max.poll.interval.ms", cfg.getMaxPollIntervalMs());
        if (cfg.getRequestTimeoutMs() != null) props.put("request.timeout.ms", cfg.getRequestTimeoutMs());
        if (cfg.getRetries() != null) props.put("retries", cfg.getRetries());
        if (cfg.getRetryBackoffMs() != null) props.put("retry.backoff.ms", cfg.getRetryBackoffMs());
        if (cfg.getIsolationLevel() != null) props.put("isolation.level", cfg.getIsolationLevel());
        if (cfg.getAllowAutoCreateTopics() != null) props.put("allow.auto.create.topics", cfg.getAllowAutoCreateTopics());
        if (cfg.getClientId() != null) props.put("client.id", cfg.getClientId());

        // Security and authentication
        if (cfg.getSecurityProtocol() != null) props.put("security.protocol", cfg.getSecurityProtocol());
        if (cfg.getSaslMechanism() != null) props.put("sasl.mechanism", cfg.getSaslMechanism());
        if (cfg.getUsername() != null) {
            props.put("sasl.jaas.config",
                    String.format("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";", cfg.getUsername(), cfg.getPassword())
            );
        }
        if (cfg.getSslTruststoreLocation() != null) props.put("ssl.truststore.location", cfg.getSslTruststoreLocation());
        if (cfg.getSslTruststorePassword() != null) props.put("ssl.truststore.password", cfg.getSslTruststorePassword());
        if (cfg.getSslKeystoreLocation() != null) props.put("ssl.keystore.location", cfg.getSslKeystoreLocation());
        if (cfg.getSslKeystorePassword() != null) props.put("ssl.keystore.password", cfg.getSslKeystorePassword());
        if (cfg.getSslKeyPassword() != null) props.put("ssl.key.password", cfg.getSslKeyPassword());

        // Deserializers
        if (cfg.getKeyDeserializer() != null) props.put("key.deserializer", cfg.getKeyDeserializer());
        if (cfg.getValueDeserializer() != null) props.put("value.deserializer", cfg.getValueDeserializer());

        // Any additional advanced/override properties
        if (cfg.getProperties() != null) props.putAll(cfg.getProperties());

        return props;
    }
}