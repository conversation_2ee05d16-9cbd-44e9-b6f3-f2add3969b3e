package com.dell.it.hip.util.dataformatUtils;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIStreamEvent;
import io.xlate.edi.stream.EDIStreamReader;

/**
 * Utility for extracting fields from EDI X12 using STAEDI.
 */
public class StaediUtil {
    /**
     * Extract a field value from EDI using a path such as "ISA.6", "GS.2", etc.
     * Path: "SEGMENT.POS" (e.g., ISA.6, GS.2, etc.)
     */
    public static String extractField(String edi, String path) {
        if (edi == null || path == null) return null;
        String[] parts = path.split("\\.");
        if (parts.length != 2) return null; // Expect segmentName.elementIndex
        String segment = parts[0];
        int elementIdx;
        try {
            elementIdx = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            return null;
        }
        try {
            EDIInputFactory factory = EDIInputFactory.newFactory();
            ByteArrayInputStream input = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
            EDIStreamReader reader = factory.createEDIStreamReader(input);

            while (reader.hasNext()) {
                EDIStreamEvent event = reader.next();
                if (event == EDIStreamEvent.START_SEGMENT
                        && segment.equals(reader.getText())) {
                    int count = 0;
                    while (reader.hasNext()) {
                        event = reader.next();
                        if (event == EDIStreamEvent.ELEMENT_DATA) {
                            count++;
                            if (count == elementIdx) {
                                return reader.getText();
                            }
                        } else if (event == EDIStreamEvent.END_SEGMENT) {
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Optionally log error
        }
        return null;
    }
}