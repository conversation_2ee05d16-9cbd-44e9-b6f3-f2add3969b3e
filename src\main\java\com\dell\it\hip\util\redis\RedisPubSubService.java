package com.dell.it.hip.util.redis;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;

import com.dell.it.hip.util.HIPRedisKeyUtil;

@Service
@ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true")
public class RedisPubSubService implements MessageListener {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private RedisMessageListenerContainer container;

    // Map: topic -> callback (cluster refill, throttle changed, hold changed, etc)
    private final Map<String, Consumer<String>> listeners = new ConcurrentHashMap<>();

    public RedisPubSubService() {}


    public void publishRefill(String serviceManagerName, String integrationId, String integrationVersion) {
        String topic = HIPRedisKeyUtil.clusterRefillTopic(serviceManagerName, integrationId, integrationVersion);
        redisTemplate.convertAndSend(topic, "REFILL");
        logger.info("Published cluster-wide throttle refill for {}", topic);
    }
    public void registerRefillListener(String serviceManagerName, String integrationId, String integrationVersion, Consumer<String> callback) {
        String topic = HIPRedisKeyUtil.clusterRefillTopic(serviceManagerName, integrationId, integrationVersion);
        registerListener(topic, callback);
    }


    public void publishThrottleChanged(String serviceManagerName, String integrationId, String integrationVersion) {
        String topic = HIPRedisKeyUtil.throttleChangedTopic(serviceManagerName, integrationId, integrationVersion);
        redisTemplate.convertAndSend(topic, "CHANGED");
        logger.info("Published cluster-wide throttle changed event for {}", topic);
    }
    public void registerThrottleChangedListener(String serviceManagerName, String integrationId, String integrationVersion, Consumer<String> callback) {
        String topic = HIPRedisKeyUtil.throttleChangedTopic(serviceManagerName, integrationId, integrationVersion);
        registerListener(topic, callback);
    }


    public void publishHoldChanged(String serviceManagerName, String integrationId, String integrationVersion, boolean hold) {
        String topic = HIPRedisKeyUtil.clusterHoldChangedTopic(serviceManagerName, integrationId, integrationVersion);
        redisTemplate.convertAndSend(topic, hold ? "HOLD" : "RESUME");
        logger.info("Published cluster-wide hold event for {} = {}", topic, hold);
    }
    public void registerHoldChangedListener(String serviceManagerName, String integrationId, String integrationVersion, Consumer<Boolean> callback) {
        String topic = HIPRedisKeyUtil.clusterHoldChangedTopic(serviceManagerName, integrationId, integrationVersion);
        if (!listeners.containsKey(topic)) {
            MessageListener listener = (message, pattern) -> {
                String body = new String(message.getBody(), StandardCharsets.UTF_8);
                boolean isHold = "HOLD".equalsIgnoreCase(body);
                callback.accept(isHold);
            };
            container.addMessageListener(listener, new ChannelTopic(topic));
            listeners.put(topic, body -> callback.accept("HOLD".equalsIgnoreCase(body)));
        }
    }

    // ---- Generic Listener ----
    public void registerListener(String topic, Consumer<String> callback) {
        if (!listeners.containsKey(topic)) {
            MessageListener listener = (message, pattern) -> {
                String body = new String(message.getBody(), StandardCharsets.UTF_8);
                callback.accept(body);
            };
            container.addMessageListener(listener, new ChannelTopic(topic));
            listeners.put(topic, callback);
        }
    }

    // ---- Direct Redis get/set (optional utility) ----
    public String get(String key) { return redisTemplate.opsForValue().get(key); }
    public void set(String key, String value) { redisTemplate.opsForValue().set(key, value); }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        // (Unused, for interface only. Use registerListener for all handling.)
    }
}