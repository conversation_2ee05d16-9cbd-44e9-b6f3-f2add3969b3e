package com.dell.it.hip.strategy.flows.rules;

import java.util.Map;

import org.springframework.messaging.Message;

public class StopRuleAction implements RuleAction {
    @Override
    public String getName() { return "stop"; }
    @Override
    public String getType() { return "stop"; }
    @Override
    public Message<?> performAction(Message<?> msg, Map<String, Object> context) {
        // No-op: used only as a marker to halt rule processing
        return msg;
    }
}