package com.dell.it.hip.config.FlowSteps;

public class SplitterFlowStepConfig extends FlowStepConfig {

    // ==== Generic splitting ====
    private boolean genericSplitting = false;
    private String splitterRegex;

    // ==== EDI X12 options ====
    private boolean splitX12 = false;
    private String x12SplitLevel; // e.g. "interchange", "group", "transaction"
    private String x12SegmentDelimiter;
    private String x12ElementDelimiter;
    private String x12SubElementDelimiter;
    private boolean allowMultipleInterchanges = false;

    // ==== EDI EDIFACT options ====
    private boolean splitEdifact = false;
    private String edifactSplitLevel; // e.g. "interchange", "message"
    private String edifactSegmentDelimiter;
    private String edifactElementDelimiter;
    private String edifactSubElementDelimiter;
    private boolean allowMultipleEdifactInterchanges = false;

    // ==== XML options ====
    private boolean splitXml = false;
    private String xmlXPathExpression;

    // ==== CSV options ====
    private boolean splitCsvLines = false;

    // ==== JSON options ====
    private boolean splitJsonArray = false;
    private String jsonPathExpression;

    // ==== Format ====
    private String inputFormat; // If user wants to override auto-detect (e.g. "EDI_X12", "XML", ...)

    // ==== Header options ====
    private boolean copyHeaders = true;

    // --- Getters and Setters ---

    public boolean isGenericSplitting() {
        return genericSplitting;
    }

    public void setGenericSplitting(boolean genericSplitting) {
        this.genericSplitting = genericSplitting;
    }

    public String getSplitterRegex() {
        return splitterRegex;
    }

    public void setSplitterRegex(String splitterRegex) {
        this.splitterRegex = splitterRegex;
    }

    public boolean isSplitX12() {
        return splitX12;
    }

    public void setSplitX12(boolean splitX12) {
        this.splitX12 = splitX12;
    }

    public String getX12SplitLevel() {
        return x12SplitLevel;
    }

    public void setX12SplitLevel(String x12SplitLevel) {
        this.x12SplitLevel = x12SplitLevel;
    }

    public String getX12SegmentDelimiter() {
        return x12SegmentDelimiter;
    }

    public void setX12SegmentDelimiter(String x12SegmentDelimiter) {
        this.x12SegmentDelimiter = x12SegmentDelimiter;
    }

    public String getX12ElementDelimiter() {
        return x12ElementDelimiter;
    }

    public void setX12ElementDelimiter(String x12ElementDelimiter) {
        this.x12ElementDelimiter = x12ElementDelimiter;
    }

    public String getX12SubElementDelimiter() {
        return x12SubElementDelimiter;
    }

    public void setX12SubElementDelimiter(String x12SubElementDelimiter) {
        this.x12SubElementDelimiter = x12SubElementDelimiter;
    }

    public boolean isAllowMultipleInterchanges() {
        return allowMultipleInterchanges;
    }

    public void setAllowMultipleInterchanges(boolean allowMultipleInterchanges) {
        this.allowMultipleInterchanges = allowMultipleInterchanges;
    }

    public boolean isSplitEdifact() {
        return splitEdifact;
    }

    public void setSplitEdifact(boolean splitEdifact) {
        this.splitEdifact = splitEdifact;
    }

    public String getEdifactSplitLevel() {
        return edifactSplitLevel;
    }

    public void setEdifactSplitLevel(String edifactSplitLevel) {
        this.edifactSplitLevel = edifactSplitLevel;
    }

    public String getEdifactSegmentDelimiter() {
        return edifactSegmentDelimiter;
    }

    public void setEdifactSegmentDelimiter(String edifactSegmentDelimiter) {
        this.edifactSegmentDelimiter = edifactSegmentDelimiter;
    }

    public String getEdifactElementDelimiter() {
        return edifactElementDelimiter;
    }

    public void setEdifactElementDelimiter(String edifactElementDelimiter) {
        this.edifactElementDelimiter = edifactElementDelimiter;
    }

    public String getEdifactSubElementDelimiter() {
        return edifactSubElementDelimiter;
    }

    public void setEdifactSubElementDelimiter(String edifactSubElementDelimiter) {
        this.edifactSubElementDelimiter = edifactSubElementDelimiter;
    }

    public boolean isAllowMultipleEdifactInterchanges() {
        return allowMultipleEdifactInterchanges;
    }

    public void setAllowMultipleEdifactInterchanges(boolean allowMultipleEdifactInterchanges) {
        this.allowMultipleEdifactInterchanges = allowMultipleEdifactInterchanges;
    }

    public boolean isSplitXml() {
        return splitXml;
    }

    public void setSplitXml(boolean splitXml) {
        this.splitXml = splitXml;
    }

    public String getXmlXPathExpression() {
        return xmlXPathExpression;
    }

    public void setXmlXPathExpression(String xmlXPathExpression) {
        this.xmlXPathExpression = xmlXPathExpression;
    }

    public boolean isSplitCsvLines() {
        return splitCsvLines;
    }

    public void setSplitCsvLines(boolean splitCsvLines) {
        this.splitCsvLines = splitCsvLines;
    }

    public boolean isSplitJsonArray() {
        return splitJsonArray;
    }

    public void setSplitJsonArray(boolean splitJsonArray) {
        this.splitJsonArray = splitJsonArray;
    }

    public String getJsonPathExpression() {
        return jsonPathExpression;
    }

    public void setJsonPathExpression(String jsonPathExpression) {
        this.jsonPathExpression = jsonPathExpression;
    }

    public String getInputFormat() {
        return inputFormat;
    }

    public void setInputFormat(String inputFormat) {
        this.inputFormat = inputFormat;
    }

    public boolean isCopyHeaders() {
        return copyHeaders;
    }

    public void setCopyHeaders(boolean copyHeaders) {
        this.copyHeaders = copyHeaders;
    }
}