package com.dell.it.hip.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;

import jakarta.annotation.PostConstruct;

@Component
public class ClusterEventListenerConfig {

    @Autowired
    private HIPClusterCoordinationService clusterCoordinationService;
    @Autowired
    private HIPIntegrationOrchestrationService orchestrationService;

    @PostConstruct
    public void init() {
        clusterCoordinationService.registerPauseChangedListener(orchestrationService::onPauseChangedEvent);
        clusterCoordinationService.registerThrottleUpdateListener(orchestrationService::onThrottleUpdateEvent);
        clusterCoordinationService.registerRegistrationListener(orchestrationService::onRegistrationEvent);
        clusterCoordinationService.registerUnregistrationListener(orchestrationService::onUnregistrationEvent);
        clusterCoordinationService.registerCustomEventListener(orchestrationService::onCustomClusterEvent);
    }
}