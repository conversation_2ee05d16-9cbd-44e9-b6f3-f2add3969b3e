package com.dell.it.hip.strategy.flows;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.flowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.flowSteps.SplitterFlowStepConfig;
import com.dell.it.hip.util.dataformatUtils.CsvUtil;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;
import com.dell.it.hip.util.dataformatUtils.StaediUtil;
import com.dell.it.hip.util.dataformatUtils.XmlUtil;
import com.dell.it.hip.util.validation.MessageFormatDetector;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("splitter")
public class SplitterFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired(required = false)
    private StaediUtil staediUtil;
    @Autowired(required = false)
    private XmlUtil xmlUtil;
    @Autowired(required = false)
    private CsvUtil csvUtil;
    @Autowired(required = false)
    private JsonUtil jsonUtil;

    @Override
    public String getType() {
        return "splitter";
    }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        SplitterFlowStepConfig config = (SplitterFlowStepConfig) def.getConfigMap().get(stepConfigRef.getPropertyRef());
        if (config == null) throw new IllegalStateException("No Splitter config for: " + stepConfigRef.getPropertyRef());

        String payload = Objects.toString(message.getPayload(), "");
        String fmt = (config.getInputFormat() != null) ? config.getInputFormat() : MessageFormatDetector.detect(payload);
        List<String> parts = null;

        // ==== EDI X12 ====
        if (staediUtil == null) staediUtil = new StaediUtil();
        if (config.isSplitX12() && "EDI_X12".equalsIgnoreCase(fmt)) {
            String segDelim = Optional.ofNullable(config.getX12SegmentDelimiter()).orElseGet(() -> staediUtil.detectX12SegmentDelimiter(payload));
            String elemDelim = Optional.ofNullable(config.getX12ElementDelimiter()).orElseGet(() -> staediUtil.detectX12ElementDelimiter(payload));
            String subElemDelim = Optional.ofNullable(config.getX12SubElementDelimiter()).orElse(":");



            parts = staediUtil.splitX12(payload,
                    config.getX12SplitLevel(),
                    segDelim, elemDelim, subElemDelim,
                    config.isAllowMultipleInterchanges());
        }
        // ==== EDI EDIFACT ====
        else if (config.isSplitEdifact() && "EDI_EDIFACT".equalsIgnoreCase(fmt)) {
            String segDelim = Optional.ofNullable(config.getEdifactSegmentDelimiter()).orElse("'");
            String elemDelim = Optional.ofNullable(config.getEdifactElementDelimiter()).orElse("+");
            String subElemDelim = Optional.ofNullable(config.getEdifactSubElementDelimiter()).orElse(":");

            if (staediUtil == null) staediUtil = new StaediUtil();

            parts = staediUtil.splitEdifactMessages(payload,
                    config.getEdifactSplitLevel(),
                    segDelim, elemDelim, subElemDelim,
                    config.isAllowMultipleEdifactInterchanges());
        }
        // ==== XML ====
        else if (config.isSplitXml() && "XML".equalsIgnoreCase(fmt)) {
            if (xmlUtil == null) xmlUtil = new XmlUtil();
            parts = xmlUtil.splitByXPath(payload, config.getXmlXPathExpression());
        }
        // ==== CSV ====
        else if (config.isSplitCsvLines() && "CSV".equalsIgnoreCase(fmt)) {
            if (csvUtil == null) csvUtil = new CsvUtil();
            parts = csvUtil.splitLines(payload);
        }
        // ==== JSON ====
        else if (config.isSplitJsonArray() && "JSON".equalsIgnoreCase(fmt)) {
            if (jsonUtil == null) jsonUtil = new JsonUtil();
            parts = jsonUtil.splitByJsonPath(payload, config.getJsonPathExpression());
        }
        // ==== Generic Regex ====
        else if (config.isGenericSplitting() && config.getSplitterRegex() != null) {
            parts = Arrays.asList(payload.split(config.getSplitterRegex()));
        }
        // ==== Fallback (no splitting) ====
        else {
            parts = Collections.singletonList(payload);
        }

        if (parts == null || parts.isEmpty()) {
            log.warn("Splitter produced no parts. Sending to error/termination channels.");
            wiretapService.tap(
                    message, def, stepConfigRef,
                    "error", "Splitter produced no parts"
            );

            transactionLoggingUtil.logError(message, def, stepConfigRef, "SplitterError","Splitter produced no parts");
            return Collections.emptyList();
        }

        // ==== Build result messages ====
        List<Message<?>> result = new ArrayList<>();
        for (String part : parts) {
            MessageBuilder<Object> builder = MessageBuilder.withPayload(part);
            if (config.isCopyHeaders()) builder.copyHeaders(message.getHeaders());
            builder.setHeader("splitter_original_format", fmt);
            builder.setHeader("splitter_step_ref", stepConfigRef.getPropertyRef());
            result.add(builder.build());
        }

        // Log successful split
        wiretapService.tap(
                message, def, stepConfigRef,
                "info", "Splitter produced " + result.size() + " messages"
        );
        transactionLoggingUtil.logInfo(message, def, stepConfigRef, "Splitter produced " + result.size() + " messages");

        return result;
    }
}
