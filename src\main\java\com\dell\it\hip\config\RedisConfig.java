package com.dell.it.hip.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import redis.embedded.RedisServer;

@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    /**
     * Embedded Redis Server for development/testing environments.
     * Only starts when no external Redis is available or when explicitly enabled.
     */
    @Bean(initMethod = "start", destroyMethod = "stop")
    @ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true", matchIfMissing = false)
    @Profile({"dev", "test", "local"})
    public RedisServer embeddedRedisServer() {
        logger.info("Starting embedded Redis server on port 6370 for development");
        return RedisServer.builder()
                .port(6370)
                .setting("maxmemory 128M")
                .build();
    }

    /**
     * Redis Connection Factory - uses Spring Boot auto-configuration by default.
     * Falls back to localhost:6379 if no configuration is provided.
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public LettuceConnectionFactory redisConnectionFactory() {
        // This will use Spring Boot's auto-configuration from application.yaml
        // If no configuration is found, it defaults to localhost:6379
        return new LettuceConnectionFactory();
    }

    /**
     * Fallback Redis Connection Factory for development.
     * Only used when external Redis is not available.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true")
    public LettuceConnectionFactory embeddedRedisConnectionFactory() {
        logger.info("Configuring Redis connection for embedded Redis server");
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName("localhost");
        config.setPort(6379);
        return new LettuceConnectionFactory(config);
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        return new StringRedisTemplate(redisConnectionFactory);
    }

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }
}