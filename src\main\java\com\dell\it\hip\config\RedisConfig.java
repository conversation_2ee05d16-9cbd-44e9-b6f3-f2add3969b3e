package com.dell.it.hip.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import redis.embedded.RedisServer;

@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    /**
     * Embedded Redis Server for development/testing environments.
     * Only starts when explicitly enabled and not on Windows (due to compatibility issues).
     */
    @Bean(initMethod = "start", destroyMethod = "stop")
    @ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true")
    @ConditionalOnProperty(name = "os.name", havingValue = "Linux", matchIfMissing = false)
    public RedisServer embeddedRedisServer() {
        logger.info("Starting embedded Redis server on port 6370 for development");
        return RedisServer.builder()
                .port(6370)
                .setting("maxmemory 128M")
                .build();
    }

    /**
     * Redis Connection Factory for embedded Redis.
     * Only created when embedded Redis is enabled and running.
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true")
    @ConditionalOnProperty(name = "os.name", havingValue = "Linux", matchIfMissing = false)
    public LettuceConnectionFactory embeddedRedisConnectionFactory() {
        logger.info("Configuring Redis connection for embedded Redis server on port 6370");
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName("localhost");
        config.setPort(6370);
        return new LettuceConnectionFactory(config);
    }

    /**
     * StringRedisTemplate - only created when embedded Redis is enabled and running.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true")
    @ConditionalOnProperty(name = "os.name", havingValue = "Linux", matchIfMissing = false)
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        return new StringRedisTemplate(redisConnectionFactory);
    }

    /**
     * Redis Message Listener Container - only created when embedded Redis is enabled and running.
     */
    @Bean
    @DependsOn({"embeddedRedisServer"})
    @ConditionalOnProperty(name = "spring.data.redis.embedded.enabled", havingValue = "true")
    @ConditionalOnProperty(name = "os.name", havingValue = "Linux", matchIfMissing = false)
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }
}