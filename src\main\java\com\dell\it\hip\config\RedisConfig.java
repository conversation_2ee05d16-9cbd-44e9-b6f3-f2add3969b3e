package com.dell.it.hip.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

	/*
	 * @Value("${spring.data.redis.host}") private String redisHost;
	 * 
	 * @Value("${spring.data.redis.port}") private int redisPort;
	 * 
	 * @Value("${spring.data.redis.password:}") private String redisPassword;
	 */
    /**
     * Redis Connection Factory for external Redis server.
     * Only created when Redis is enabled.
     */
	/*
	 * @Bean
	 * 
	 * @Primary
	 * 
	 * @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue =
	 * "true") public LettuceConnectionFactory redisConnectionFactory() {
	 * logger.info("Configuring Redis connection for external Redis server at {}:{}"
	 * , redisHost, redisPort); RedisStandaloneConfiguration config = new
	 * RedisStandaloneConfiguration(); config.setHostName(redisHost);
	 * config.setPort(redisPort);
	 * 
	 * if (redisPassword != null && !redisPassword.trim().isEmpty()) {
	 * config.setPassword(RedisPassword.of(redisPassword)); } return new
	 * LettuceConnectionFactory(config); }
	 */

    /**
     * StringRedisTemplate - only created when Redis is enabled.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        return new StringRedisTemplate(redisConnectionFactory);
    }

    /**
     * Redis Message Listener Container - only created when Redis is enabled.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }
}