package com.dell.it.hip.util.logging;

import java.time.Instant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;

import com.dell.it.hip.config.ConfigRef;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.TransactionStatus;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;


/**
 * Utility for sending transaction-level monitoring/log events for HIP integration processing.
 * Supports events: started, completed, error, terminate, and custom.
 */
public class TransactionLoggingUtil {

    private static final Logger logger = LoggerFactory.getLogger(TransactionLoggingUtil.class);


    // === LIFECYCLE EVENT LOGGING ===

    public void sendStartedEvent(HIPIntegrationDefinition def, ConfigRef ref, String event) {
        logger.info("[STARTED][def={}:{}][adapterId={}] {}", def.getHipIntegrationName(), def.getVersion(), ref.getId(), event);
    }

    public void sendLifecycleEvent(HIPIntegrationDefinition def, ConfigRef ref, String event) {
        logger.info("[LIFECYCLE][def={}:{}][adapterId={}] {}", def.getHipIntegrationName(), def.getVersion(), ref.getId(), event);
    }

    public void sendCompletedEvent(HIPIntegrationDefinition def, ConfigRef ref, String event) {
        logger.info("[COMPLETED][def={}:{}][adapterId={}] {}", def.getHipIntegrationName(), def.getVersion(), ref.getId(), event);
    }

    public void sendTerminateEvent(HIPIntegrationDefinition def, ConfigRef ref, String reason) {
        logger.warn("[TERMINATED][def={}:{}][adapterId={}] {}", def.getHipIntegrationName(), def.getVersion(), ref.getId(), reason);
    }

    // === ERROR EVENTS ===

    public void sendErrorEvent(HIPIntegrationDefinition def, ConfigRef ref, String error, Throwable t) {
        logger.error("[ERROR][def={}:{}][adapterId={}] {}", def.getHipIntegrationName(), def.getVersion(), ref.getId(), error, t);
    }

    public static void sendErrorEvent(HIPIntegrationDefinition def, Message<?> message, Exception e) {
        String traceId = getTraceId(message);
        logger.error("[ERROR][traceId={}] Integration={} | Headers={} | Error={}", traceId, def.getHipIntegrationName(), message.getHeaders(), e.getMessage(), e);
    }

    // === HANDLER-SPECIFIC LOGGING ===

    public void sendHandlerEvent(HIPIntegrationDefinition def, HandlerConfigRef ref, String event) {
        logger.info("[HANDLER][def={}:{}][handlerRef={}] {}", def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), event);
    }

    // === MESSAGE-BASED LOGGING FOR START/COMPLETE/TERMINATE ===

    public static void sendStarted(Message<?> message, HIPIntegrationDefinition def, String event) {
        String traceId = getTraceId(message);
        logger.info("[STARTED][traceId={}] {} | Def={} | Headers={}", traceId, event, def.getHipIntegrationName(), message.getHeaders());
    }

    public static void sendCompleted(Message<?> message, HIPIntegrationDefinition def, String event) {
        String traceId = getTraceId(message);
        logger.info("[COMPLETED][traceId={}] {} | Def={} | Headers={}", traceId, event, def.getHipIntegrationName(), message.getHeaders());
    }

    public void sendTerminate(Message<?> message, HIPIntegrationDefinition def, String reason) {
        String traceId = getTraceId(message);
        logger.warn("[TERMINATED][traceId={}] {} | Def={} | Headers={}", traceId, reason, def.getHipIntegrationName(), message.getHeaders());
    }

    public void sendError(Message<?> message, HIPIntegrationDefinition def, String error, Throwable t) {
        String traceId = getTraceId(message);
        logger.error("[ERROR][traceId={}] {} | Def={} | Headers={}", traceId, error, def.getHipIntegrationName(), message.getHeaders(), t);
    }

    public void sendHandlerEvent(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref, String event) {
        String traceId = getTraceId(message);
        logger.info("[HANDLER][traceId={}] {} | HandlerRef={} | Def={}", traceId, event, ref.getPropertyRef(), def.getHipIntegrationName());
    }

    // === TRACE ID EXTRACTION (supports OTel, B3, legacy, fallback) ===

    public static String getTraceId(Message<?> message) {
        if (message == null) return "UNKNOWN";
        Object traceId = message.getHeaders().get("traceId");
        if (traceId != null) return String.valueOf(traceId);

        // OTel W3C traceparent: "00-traceid-spanid-flags"
        Object traceParent = message.getHeaders().get("traceparent");
        if (traceParent != null) {
            String parent = String.valueOf(traceParent);
            if (parent.length() >= 55) { // W3C format length
                // W3C: "00-traceid-spanid-flags"
                String[] parts = parent.split("-");
                if (parts.length > 1) return parts[1]; // traceId
            }
        }

        // B3 format (if ever used)
        Object b3 = message.getHeaders().get("X-B3-TraceId");
        if (b3 != null) return String.valueOf(b3);

        // Fallback
        return "UNKNOWN";
    }

    // If tags ever contains a traceId (legacy support)
    public static String getTraceId(HIPIntegrationDefinition def) {
        if (def == null) return "UNKNOWN";
        String tags = def.getTags();
        if (tags != null && tags.contains("traceId")) {
            // e.g., tags="...traceId=123abc..."
            int idx = tags.indexOf("traceId=");
            if (idx >= 0) {
                int end = tags.indexOf(',', idx);
                if (end < 0) end = tags.length();
                return tags.substring(idx + 8, end);
            }
        }
        return "UNKNOWN";
    }

    private TransactionLoggingUtil() {
        // static utility class
    }


    /**
     * Logs a generic transaction event for audit, compliance, or analytics.
     *
     * @param eventType      "HANDLER", "ADAPTER", "FLOWSTEP", etc.
     * @param eventName      Handler/adapter/step name or type
     * @param eventRole      "primary", "fallback", etc.
     * @param def            HIP integration definition (context)
     * @param eventConfigRef HandlerConfigRef, AdapterConfigRef, StepConfig, etc.
     * @param status         TransactionStatus (SUCCESS, FAILURE, etc.)
     * @param errorMsg       Error message if any (null for success)
     * @param message        The Spring Integration Message<?>
     */
    public static void logTermination(
            String eventType,
            String eventName,
            String eventRole,
            HIPIntegrationDefinition def,
            Object eventConfigRef,
            TransactionStatus status,
            String errorMsg,
            Message<?> message
    ) {
        try {
            GenericTransactionLogEntry entry = new GenericTransactionLogEntry();
            entry.setTimestamp(Instant.now());
            entry.setServiceManagerName(def != null ? def.getServiceManagerName() : null);
            entry.setIntegrationName(def != null ? def.getHipIntegrationName() : null);
            entry.setIntegrationVersion(def != null ? def.getVersion() : null);
            entry.setEventType(eventType);
            entry.setEventName(eventName);
            entry.setEventRole(eventRole);
            entry.setEventConfigRef(eventConfigRef);
            entry.setStatus(status);
            entry.setErrorMessage(errorMsg);
            entry.setMessageId(message != null && message.getHeaders().containsKey("id")
                    ? message.getHeaders().get("id").toString()
                    : null);
            entry.setPayload(message != null ? message.getPayload() : null);

            // Log as a single object (toString outputs all fields)
            logger.info("[TRANSACTION-TERMINATION] {}", entry);

            // TODO: Optionally persist to DB, S3, Elasticsearch, etc.
            // persistTransactionLog(entry);

        } catch (Exception e) {
            logger.error("TransactionLoggingUtil failed to log generic termination: {}", e.getMessage(), e);
        }
    }
    /**
     * Log an error event for a flow step, handler, or adapter.
     * @param message    The Spring Integration message being processed
     * @param def        The HIPIntegrationDefinition for this flow
     * @param configRef  The config ref (FlowStepConfigRef, HandlerConfigRef, AdapterConfigRef)
     * @param eventType  Usually "error"
     * @param errorMsg   Error message string
     */
    public static void logError(
            Message<?> message,
            HIPIntegrationDefinition def,
            Object configRef,
            String eventType,
            String errorMsg
    ) {
        String integrationName = def != null ? def.getHipIntegrationName() : "UNKNOWN";
        String version = def != null ? def.getVersion() : "UNKNOWN";
        String refType = configRef != null ? configRef.getClass().getSimpleName() : "UNKNOWN";
        String refId = extractRefId(configRef);

        logger.error("[ERROR][integration={}][version={}][refType={}][refId={}] [{}] {} - Headers: {}",
                integrationName, version, refType, refId, eventType, errorMsg, message != null ? message.getHeaders() : "N/A"
        );

        // Optionally emit to an error event store, audit, or monitoring system here.
    }

    /**
     * Log an informational event for a flow step, handler, or adapter.
     * @param message    The Spring Integration message being processed
     * @param def        The HIPIntegrationDefinition for this flow
     * @param configRef  The config ref (FlowStepConfigRef, HandlerConfigRef, AdapterConfigRef)
     * @param infoMsg    Info message string
     */
    public static void logInfo(
            Message<?> message,
            HIPIntegrationDefinition def,
            Object configRef,
            String infoMsg
    ) {
        String integrationName = def != null ? def.getHipIntegrationName() : "UNKNOWN";
        String version = def != null ? def.getVersion() : "UNKNOWN";
        String refType = configRef != null ? configRef.getClass().getSimpleName() : "UNKNOWN";
        String refId = extractRefId(configRef);

        logger.info("[INFO][integration={}][version={}][refType={}][refId={}] {}",
                integrationName, version, refType, refId, infoMsg
        );
        // Optionally emit to audit/event system here.
    }

    private static String extractRefId(Object configRef) {
        // Works for HandlerConfigRef, AdapterConfigRef, FlowStepConfigRef, etc.
        if (configRef == null) return "UNKNOWN";
        try {
            if (configRef instanceof ConfigRef ref) {
                return ref.getId();
            }
            var method = configRef.getClass().getMethod("getPropertyRef");
            return String.valueOf(method.invoke(configRef));
        } catch (Exception ignore) {}
        return configRef.toString();
    }



    // Generic transaction log POJO
    public static class GenericTransactionLogEntry {
        private Instant timestamp;
        private String serviceManagerName;
        private String integrationName;
        private String integrationVersion;
        private String eventType;
        private String eventName;
        private String eventRole;
        private Object eventConfigRef;
        private TransactionStatus status;
        private String errorMessage;
        private String messageId;
        private Object payload;

        // Getters and setters...

        public Instant getTimestamp() { return timestamp; }
        public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }

        public String getServiceManagerName() { return serviceManagerName; }
        public void setServiceManagerName(String serviceManagerName) { this.serviceManagerName = serviceManagerName; }

        public String getIntegrationName() { return integrationName; }
        public void setIntegrationName(String integrationName) { this.integrationName = integrationName; }

        public String getIntegrationVersion() { return integrationVersion; }
        public void setIntegrationVersion(String integrationVersion) { this.integrationVersion = integrationVersion; }

        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }

        public String getEventName() { return eventName; }
        public void setEventName(String eventName) { this.eventName = eventName; }

        public String getEventRole() { return eventRole; }
        public void setEventRole(String eventRole) { this.eventRole = eventRole; }

        public Object getEventConfigRef() { return eventConfigRef; }
        public void setEventConfigRef(Object eventConfigRef) { this.eventConfigRef = eventConfigRef; }

        public TransactionStatus getStatus() { return status; }
        public void setStatus(TransactionStatus status) { this.status = status; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }

        public Object getPayload() { return payload; }
        public void setPayload(Object payload) { this.payload = payload; }
        public void logInfo(
                Message<?> message,
                HIPIntegrationDefinition def,
                Object configRef,
                String eventType,
                String eventName
        ) {
            // You can log to SLF4J, or send a wiretap, or custom logic
            logger.info("[INFO][{}][{}] {} - {}", eventType, eventName, def.getHipIntegrationName(), message.getHeaders().getId());
        }

        public void logError(
                Message<?> message,
                HIPIntegrationDefinition def,
                Object configRef,
                String errorMsg
        ) {
            logger.error("[ERROR][{}] {} - {} - {}",def.getHipIntegrationName(), message.getHeaders().getId(), errorMsg);
        }
        @Override
        public String toString() {
            return "GenericTransactionLogEntry{" +
                    "timestamp=" + timestamp +
                    ", serviceManagerName='" + serviceManagerName + '\'' +
                    ", integrationName='" + integrationName + '\'' +
                    ", integrationVersion='" + integrationVersion + '\'' +
                    ", eventType='" + eventType + '\'' +
                    ", eventName='" + eventName + '\'' +
                    ", eventRole='" + eventRole + '\'' +
                    ", eventConfigRef=" + (eventConfigRef != null ? eventConfigRef.toString() : "null") +
                    ", status=" + status +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", messageId='" + messageId + '\'' +
                    ", payload=" + (payload != null ? payload.toString() : "null") +
                    '}';
        }
    }
}