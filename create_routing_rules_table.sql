-- Oracle SQL DDL script to create the routing_rules table
-- This table stores routing rules for the HIP integration platform

CREATE TABLE routing_rules (
    id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    rule_key VARCHAR2(255) NOT NULL,
    route_type VARCHAR2(50) NOT NULL,
    channel_name VA<PERSON>HAR2(255),
    handler_config_ref_id VARCHAR2(255),
    condition VARCHAR2(1000),
    priority NUMBER DEFAULT 0,
    enabled NUMBER(1) DEFAULT 1,
    description VARCHAR2(500),
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_routing_rules_rule_key ON routing_rules(rule_key);
CREATE INDEX idx_routing_rules_enabled ON routing_rules(enabled);
CREATE INDEX idx_routing_rules_priority ON routing_rules(priority);
CREATE INDEX idx_routing_rules_route_type ON routing_rules(route_type);

-- Composite index for the most common query pattern
CREATE INDEX idx_routing_rules_key_enabled_priority ON routing_rules(rule_key, enabled, priority);

-- Add constraints
ALTER TABLE routing_rules ADD CONSTRAINT chk_route_type 
    CHECK (route_type IN ('CHANNEL', 'HANDLER', 'NONE'));

ALTER TABLE routing_rules ADD CONSTRAINT chk_enabled 
    CHECK (enabled IN (0, 1));

-- Add comments for documentation
COMMENT ON TABLE routing_rules IS 'Stores routing rules for dynamic message routing in HIP integrations';
COMMENT ON COLUMN routing_rules.id IS 'Primary key, auto-generated';
COMMENT ON COLUMN routing_rules.rule_key IS 'Unique identifier for the routing rule group';
COMMENT ON COLUMN routing_rules.route_type IS 'Type of routing: CHANNEL, HANDLER, or NONE';
COMMENT ON COLUMN routing_rules.channel_name IS 'Target channel name (used when route_type is CHANNEL)';
COMMENT ON COLUMN routing_rules.handler_config_ref_id IS 'Handler configuration reference ID (used when route_type is HANDLER)';
COMMENT ON COLUMN routing_rules.condition IS 'SpEL expression or condition for rule evaluation';
COMMENT ON COLUMN routing_rules.priority IS 'Rule priority (lower numbers have higher priority)';
COMMENT ON COLUMN routing_rules.enabled IS 'Whether the rule is active (1) or disabled (0)';
COMMENT ON COLUMN routing_rules.description IS 'Human-readable description of the rule';
COMMENT ON COLUMN routing_rules.created_at IS 'Timestamp when the rule was created';
COMMENT ON COLUMN routing_rules.updated_at IS 'Timestamp when the rule was last updated';
