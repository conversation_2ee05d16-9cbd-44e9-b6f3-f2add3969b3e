package com.dell.it.hip.util;

public interface ThrottlingService {
    boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion, ThrottleSettings settings);
    void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion);
    void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion, ThrottleSettings settings);
    ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId, String integrationVersion);
}