package com.dell.it.hip.util.routing;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * JPA Entity for storing routing rules in the database.
 */
@Entity
@Table(name = "routing_rules")
public class RoutingRuleEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "rule_key", nullable = false, length = 255)
    private String ruleKey;

    @Enumerated(EnumType.STRING)
    @Column(name = "route_type", nullable = false, length = 50)
    private RoutingDecision.RouteType routeType;

    @Column(name = "channel_name", length = 255)
    private String channelName;

    @Column(name = "handler_config_ref_id", length = 255)
    private String handlerConfigRefId;

    @Column(name = "condition", length = 1000)
    private String condition;

    @Column(name = "priority")
    private Integer priority = 0;

    @Column(name = "enabled")
    private Boolean enabled = true;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // JPA lifecycle methods
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = this.createdAt;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Constructors
    public RoutingRuleEntity() {}

    public RoutingRuleEntity(String ruleKey, RoutingDecision.RouteType routeType) {
        this.ruleKey = ruleKey;
        this.routeType = routeType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleKey() {
        return ruleKey;
    }

    public void setRuleKey(String ruleKey) {
        this.ruleKey = ruleKey;
    }

    public RoutingDecision.RouteType getRouteType() {
        return routeType;
    }

    public void setRouteType(RoutingDecision.RouteType routeType) {
        this.routeType = routeType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getHandlerConfigRefId() {
        return handlerConfigRefId;
    }

    public void setHandlerConfigRefId(String handlerConfigRefId) {
        this.handlerConfigRefId = handlerConfigRefId;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
