package com.dell.it.hip.monitoring.metrics;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

/**
 * Service for managing HIP-specific metrics using Micrometer.
 */
@Service
public class HipMetricsService {

    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<String, AtomicLong> gaugeValues = new ConcurrentHashMap<>();

    @Autowired
    public HipMetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        initializeMetrics();
    }

    private void initializeMetrics() {
        // Initialize common gauges
        registerGauge("hip.integrations.active.count", "Number of active integrations");
        registerGauge("hip.messages.queue.depth", "Current message queue depth");
        registerGauge("hip.throttle.active.count", "Number of active throttles");
    }

    /**
     * Record message processing latency for a specific integration.
     */
    public Timer.Sample recordMessageProcessingStart(String integrationName, String version) {
        return Timer.start(meterRegistry);
    }

    public void recordMessageProcessingEnd(Timer.Sample sample, String integrationName, String version, String status) {
        sample.stop(Timer.builder("hip.message.processing.duration")
                .description("Message processing duration")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("status", status)
                .register(meterRegistry));
    }

    /**
     * Increment message throughput counter.
     */
    public void incrementMessageThroughput(String integrationName, String version, String adapterType) {
        Counter.builder("hip.message.throughput")
                .description("Message throughput counter")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("adapter", adapterType)
                .register(meterRegistry)
                .increment();
    }

    /**
     * Increment error counter.
     */
    public void incrementErrorCount(String integrationName, String version, String component, String errorType) {
        Counter.builder("hip.errors.count")
                .description("Error count by component and type")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("component", component)
                .tag("error_type", errorType)
                .register(meterRegistry)
                .increment();
    }

    /**
     * Record external system response time.
     */
    public void recordExternalSystemResponseTime(String systemName, String endpoint, long responseTimeMs, String status) {
        Timer.builder("hip.external.system.response.time")
                .description("External system response time")
                .tag("system", systemName)
                .tag("endpoint", endpoint)
                .tag("status", status)
                .register(meterRegistry)
                .record(responseTimeMs, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    /**
     * Increment throttle activation counter.
     */
    public void incrementThrottleActivation(String integrationName, String version, String reason) {
        Counter.builder("hip.throttle.activations")
                .description("Throttle activation count")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("reason", reason)
                .register(meterRegistry)
                .increment();
    }

    /**
     * Update queue depth gauge.
     */
    public void updateQueueDepth(String queueName, long depth) {
        String gaugeName = "hip.queue.depth." + queueName;
        gaugeValues.computeIfAbsent(gaugeName, k -> {
            AtomicLong value = new AtomicLong(0);
            Gauge.builder(gaugeName, value, AtomicLong::get)
                    .description("Queue depth for " + queueName)
                    .tag("queue", queueName)
                    .register(meterRegistry);
            return value;
        }).set(depth);
    }

    /**
     * Update active integration count.
     */
    public void updateActiveIntegrationCount(long count) {
        updateGaugeValue("hip.integrations.active.count", count);
    }

    /**
     * Update active throttle count.
     */
    public void updateActiveThrottleCount(long count) {
        updateGaugeValue("hip.throttle.active.count", count);
    }

    /**
     * Record adapter-specific metrics.
     */
    public void recordAdapterMetrics(String adapterType, String operation, long duration, String status) {
        Timer.builder("hip.adapter.operation.duration")
                .description("Adapter operation duration")
                .tag("adapter_type", adapterType)
                .tag("operation", operation)
                .tag("status", status)
                .register(meterRegistry)
                .record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    private void registerGauge(String name, String description) {
        AtomicLong value = new AtomicLong(0);
        gaugeValues.put(name, value);
        Gauge.builder(name, value, AtomicLong::get)
                .description(description)
                .register(meterRegistry);
    }

    private void updateGaugeValue(String gaugeName, long value) {
        AtomicLong gaugeValue = gaugeValues.get(gaugeName);
        if (gaugeValue != null) {
            gaugeValue.set(value);
        }
    }
}
