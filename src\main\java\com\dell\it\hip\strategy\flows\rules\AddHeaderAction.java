package com.dell.it.hip.strategy.flows.rules;

import com.dell.it.hip.strategy.flows.rules.RuleAction;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.util.Map;

public class AddHeaderAction implements RuleAction {
    private String name;
    private String type;
    private String header;
    private String value;

    @Override
    public String getName() { return name; }
    @Override
    public String getType() { return type; }

    @Override
    public Message<?> performAction(Message<?> msg, Map<String, Object> params) {
        return MessageBuilder.fromMessage(msg)
                .setHeader(header, value)
                .build();
    }
}
