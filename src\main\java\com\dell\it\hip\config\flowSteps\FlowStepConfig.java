package com.dell.it.hip.config.flowSteps;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Configuration for a single Flow Step in the HIP Integration flow.
 */
public class FlowStepConfig implements Serializable {
    private String propertyRef;   // Unique reference key for this step config (used in configMap)
    private String type;          // Step type (e.g., "transform", "router", "validate", etc.)
    private String id;            // Unique ID for this step instance
    private String role;          // Optional: "primary", "fallback", etc. (used if required in advanced flows)
    private Map<String, Object> parameters; // Step-specific parameters (transform expressions, validation settings, etc.)
    private List<String> rules;
    // === Constructors ===

    public FlowStepConfig() {}

    public FlowStepConfig(String propertyRef, String type, String id, String role, Map<String, Object> parameters) {
        this.propertyRef = propertyRef;
        this.type = type;
        this.id = id;
        this.role = role;
        this.parameters = parameters;
    }

    // === Getters and Setters ===

    public String getPropertyRef() {
        return propertyRef;
    }

    public void setPropertyRef(String propertyRef) {
        this.propertyRef = propertyRef;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public List<String> getRules() {
        return rules;
    }

    public void setRules(List<String> rules) {
        this.rules = rules;
    }
    // === equals/hashCode for collection management and logging ===

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FlowStepConfig)) return false;
        FlowStepConfig that = (FlowStepConfig) o;
        return Objects.equals(propertyRef, that.propertyRef)
                && Objects.equals(type, that.type)
                && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(propertyRef, type, id);
    }

    @Override
    public String toString() {
        return "FlowStepConfig{" +
                "propertyRef='" + propertyRef + '\'' +
                ", type='" + type + '\'' +
                ", id='" + id + '\'' +
                ", role='" + role + '\'' +
                '}';
    }
}