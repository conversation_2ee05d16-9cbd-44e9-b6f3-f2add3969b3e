package com.dell.it.hip.strategy.handlers;


import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Abstract base for all output handler strategies (Kafka, SFTP, IBM MQ, etc.)
 * Handles pause/resume, wiretap, termination logging, archiving, retries, and consistent keying.
 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.Message;
import org.springframework.retry.support.RetryTemplate;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.TransactionStatus;
import com.dell.it.hip.config.Handlers.HandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

import jakarta.annotation.PostConstruct;

public abstract class AbstractOutputHandlerStrategy implements HandlerStrategy {

    protected final OpenTelemetryPropagationUtil otelUtil;
    protected final WiretapService wiretapService;
    protected final ArchiveService archiveService;
    protected final RetryTemplateFactory retryTemplateFactory;

    protected final ConcurrentMap<String, AtomicBoolean> pauseMap = new ConcurrentHashMap<>();

    @Autowired
    @Lazy // To avoid circular dependency if any
    protected HIPClusterCoordinationService clusterCoordinationService;

    public AbstractOutputHandlerStrategy(OpenTelemetryPropagationUtil otelUtil,
                                         WiretapService wiretapService,
                                         ArchiveService archiveService,
                                         RetryTemplateFactory retryTemplateFactory) {
        this.otelUtil = otelUtil;
        this.wiretapService = wiretapService;
        this.archiveService = archiveService;
        this.retryTemplateFactory = retryTemplateFactory;
    }

    @PostConstruct
    public void initClusterListener() {
        if (clusterCoordinationService != null) {
            clusterCoordinationService.registerClusterEventListener(event -> {
                if (!event.isHandlerTarget()) return; // Only process handler-scoped events

                // Parse event target for handler
                String integrationName = event.getIntegrationName();
                String version = event.getIntegrationVersion();
                String handlerId = event.getTargetId();

                switch (event.getEventType()) {
                    case "PAUSE" -> this.pauseById(integrationName, version, handlerId);
                    case "RESUME" -> this.resumeById(integrationName, version, handlerId);
                    case "SHUTDOWN" -> this.shutdownById(integrationName, version, handlerId);
                    // Add other events as needed
                }
            });
        }
    }

    // Pause handler using integration, version, handlerId
    protected void pauseById(String integrationName, String version, String handlerId) {
        String handlerKey = buildHandlerKey(integrationName, version, handlerId);
        pauseMap.computeIfAbsent(handlerKey, k -> new AtomicBoolean(true)).set(true);
    }

    // Resume handler using integration, version, handlerId
    protected void resumeById(String integrationName, String version, String handlerId) {
        String handlerKey = buildHandlerKey(integrationName, version, handlerId);
        pauseMap.computeIfAbsent(handlerKey, k -> new AtomicBoolean(false)).set(false);
    }

    // Shutdown handler by ID (can be extended for resource cleanup)
    protected void shutdownById(String integrationName, String version, String handlerId) {
        // Implement resource cleanup if needed
    }

    @Override
    public void handle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        String handlerKey = buildHandlerKey(def, ref);

        if (isPaused(def, ref)) {
            wiretapService.tap(message, def, ref, "info", "Handler is paused: " + handlerKey);
            throw new IllegalStateException("Handler is paused: " + handlerKey);
        }

        otelUtil.propagate(message);

        wiretapService.tap(message, def, ref, "info", "Message received by handler: " + handlerKey);
        HandlerConfig config = def.getConfig(ref.getId(), HandlerConfig.class);
        if (isArchiveEnabled(config)) {
            archiveService.archive(message, def, ref);
        }

        boolean success = false;
        Exception error = null;

        try {
            RetryTemplate retryTemplate = getRetryTemplate(config);
            if (retryTemplate != null) {
                retryTemplate.execute(context -> {
                    doHandle(message, def, ref);
                    return null;
                });
            } else {
                doHandle(message, def, ref);
            }
            success = true;
            wiretapService.tap(message, def, ref, "completed", "Message sent successfully by handler: " + handlerKey);
        } catch (Exception ex) {
            error = ex;
            wiretapService.tap(message, def, ref, "error", "Handler error in: " + handlerKey + " | " + ex.getMessage());
            throw ex;
        } finally {
            wiretapService.tap(message, def, ref, "terminated",
                    success ? "Handler terminated (success): " + handlerKey
                            : "Handler terminated (error): " + handlerKey);

            TransactionStatus status = success ? TransactionStatus.SUCCESS : TransactionStatus.FAILURE;
            TransactionLoggingUtil.logTermination(
                    "HANDLER",
                    ref != null ? ref.getType() : null,
                    ref != null ? ref.getRole() : null,
                    def,
                    ref,
                    status,
                    (error != null) ? error.getMessage() : null,
                    message
            );
        }
    }

    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String handlerKey = buildHandlerKey(def, ref);
        pauseMap.computeIfAbsent(handlerKey, k -> new AtomicBoolean(true)).set(true);
        if (clusterCoordinationService != null) {
            clusterCoordinationService.pauseHandler(def, ref);
        }
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String handlerKey = buildHandlerKey(def, ref);
        pauseMap.computeIfAbsent(handlerKey, k -> new AtomicBoolean(false)).set(false);
        if (clusterCoordinationService != null) {
            clusterCoordinationService.resumeHandler(def, ref);
        }
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String handlerKey = buildHandlerKey(def, ref);
        // Prefer cluster/Redis state
        if (clusterCoordinationService != null) {
            Boolean clusterPaused = clusterCoordinationService.isHandlerPaused(def, ref);
            if (clusterPaused != null) return clusterPaused;
        }
        return pauseMap.getOrDefault(handlerKey, new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        // Subclasses may override for cleanup
        if (clusterCoordinationService != null) {
            clusterCoordinationService.shutdownHandler(def, ref);
        }
    }

    @Override
    public void dispose() {
        // Optionally clean up resources for all handlers.
    }

    protected String buildHandlerKey(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String smName = def != null ? def.getServiceManagerName() : "unknownServiceManager";
        String integrationName = def != null ? def.getHipIntegrationName() : "unknownIntegration";
        String version = def != null ? def.getVersion() : "unknownVersion";
        String handlerId = ref != null ? ref.getId() : "unknownRef";
        return buildHandlerKey(integrationName, version, handlerId);
    }

    protected String buildHandlerKey(String integrationName, String version, String handlerId) {
        return String.join(":", integrationName, version, handlerId);
    }

    protected boolean isArchiveEnabled(HandlerConfig config) {
        Object val = config.getParameters() != null ? config.getParameters().get("archiveEnabled") : null;
        return val != null && Boolean.parseBoolean(val.toString());
    }

    protected RetryTemplate getRetryTemplate(HandlerConfig config) {
        if (config.getRetrySettings() != null) {
            return retryTemplateFactory.create(config.getRetrySettings());
        }
        return null;
    }

    protected Object getParameter(HandlerConfig config, String key) {
        return config.getParameters() != null ? config.getParameters().get(key) : null;
    }

    protected abstract void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception;
}