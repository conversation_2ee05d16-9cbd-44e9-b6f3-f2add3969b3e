package com.dell.it.hip.monitoring.health;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

/**
 * Health indicator for SFTP server connectivity.
 */
@Component
public class SftpHealthIndicator implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(SftpHealthIndicator.class);

    @Value("${hip.health.sftp-servers:}")
    private List<String> sftpServers;

    @Value("${hip.health.sftp-timeout:5000}")
    private int connectionTimeout;

    @Override
    public Health health() {
        Map<String, Object> details = new HashMap<>();
        boolean allHealthy = true;

        if (sftpServers == null || sftpServers.isEmpty()) {
            return Health.up()
                    .withDetail("sftp-servers", "No SFTP servers configured for health check")
                    .build();
        }

        for (String serverConfig : sftpServers) {
            try {
                // Parse server config: host:port:username
                String[] parts = serverConfig.split(":");
                if (parts.length < 2) {
                    allHealthy = false;
                    details.put(serverConfig, Map.of(
                            "status", "DOWN",
                            "error", "Invalid server configuration format"
                    ));
                    continue;
                }

                String host = parts[0];
                int port = Integer.parseInt(parts[1]);
                String username = parts.length > 2 ? parts[2] : "testuser";

                JSch jsch = new JSch();
                Session session = jsch.getSession(username, host, port);
                session.setConfig("StrictHostKeyChecking", "no");
                session.setTimeout(connectionTimeout);

                long startTime = System.currentTimeMillis();
                session.connect();
                long responseTime = System.currentTimeMillis() - startTime;

                if (session.isConnected()) {
                    details.put(serverConfig, Map.of(
                            "status", "UP",
                            "responseTime", responseTime + "ms",
                            "host", host,
                            "port", port
                    ));
                    session.disconnect();
                } else {
                    allHealthy = false;
                    details.put(serverConfig, Map.of(
                            "status", "DOWN",
                            "reason", "Connection failed",
                            "host", host,
                            "port", port
                    ));
                }
            } catch (Exception ex) {
                allHealthy = false;
                details.put(serverConfig, Map.of(
                        "status", "DOWN",
                        "error", ex.getMessage(),
                        "reason", "Connection exception"
                ));
                logger.warn("SFTP health check failed for server: {}", serverConfig, ex);
            }
        }

        Health.Builder healthBuilder = allHealthy ? Health.up() : Health.down();
        return healthBuilder
                .withDetail("sftp-servers", details)
                .build();
    }
}
