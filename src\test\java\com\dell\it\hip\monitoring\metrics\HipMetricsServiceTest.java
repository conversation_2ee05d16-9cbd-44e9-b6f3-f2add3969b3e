package com.dell.it.hip.monitoring.metrics;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

/**
 * Unit tests for HipMetricsService.
 */
@ExtendWith(MockitoExtension.class)
class HipMetricsServiceTest {

    private MeterRegistry meterRegistry;
    private HipMetricsService hipMetricsService;

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        hipMetricsService = new HipMetricsService(meterRegistry);
    }

    @Test
    void testRecordMessageProcessing() {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        String status = "success";

        // Act
        Timer.Sample sample = hipMetricsService.recordMessageProcessingStart(integrationName, version);
        assertNotNull(sample);
        
        // Simulate some processing time
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        hipMetricsService.recordMessageProcessingEnd(sample, integrationName, version, status);

        // Assert
        Timer timer = meterRegistry.find("hip.message.processing.duration")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("status", status)
                .timer();
        
        assertNotNull(timer);
        assertEquals(1, timer.count());
        assertTrue(timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS) > 0);
    }

    @Test
    void testIncrementMessageThroughput() {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        String adapterType = "http";

        // Act
        hipMetricsService.incrementMessageThroughput(integrationName, version, adapterType);
        hipMetricsService.incrementMessageThroughput(integrationName, version, adapterType);

        // Assert
        Counter counter = meterRegistry.find("hip.message.throughput")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("adapter", adapterType)
                .counter();
        
        assertNotNull(counter);
        assertEquals(2.0, counter.count());
    }

    @Test
    void testIncrementErrorCount() {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        String component = "handler";
        String errorType = "timeout";

        // Act
        hipMetricsService.incrementErrorCount(integrationName, version, component, errorType);

        // Assert
        Counter counter = meterRegistry.find("hip.errors.count")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("component", component)
                .tag("error_type", errorType)
                .counter();
        
        assertNotNull(counter);
        assertEquals(1.0, counter.count());
    }

    @Test
    void testRecordExternalSystemResponseTime() {
        // Arrange
        String systemName = "external-api";
        String endpoint = "/api/v1/data";
        long responseTimeMs = 150;
        String status = "success";

        // Act
        hipMetricsService.recordExternalSystemResponseTime(systemName, endpoint, responseTimeMs, status);

        // Assert
        Timer timer = meterRegistry.find("hip.external.system.response.time")
                .tag("system", systemName)
                .tag("endpoint", endpoint)
                .tag("status", status)
                .timer();
        
        assertNotNull(timer);
        assertEquals(1, timer.count());
        assertEquals(responseTimeMs, timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS), 1.0);
    }

    @Test
    void testIncrementThrottleActivation() {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        String reason = "rate_limit_exceeded";

        // Act
        hipMetricsService.incrementThrottleActivation(integrationName, version, reason);

        // Assert
        Counter counter = meterRegistry.find("hip.throttle.activations")
                .tag("integration", integrationName)
                .tag("version", version)
                .tag("reason", reason)
                .counter();
        
        assertNotNull(counter);
        assertEquals(1.0, counter.count());
    }

    @Test
    void testUpdateQueueDepth() {
        // Arrange
        String queueName = "message-queue";
        long depth = 25;

        // Act
        hipMetricsService.updateQueueDepth(queueName, depth);

        // Assert
        Gauge gauge = meterRegistry.find("hip.queue.depth." + queueName)
                .tag("queue", queueName)
                .gauge();
        
        assertNotNull(gauge);
        assertEquals(depth, gauge.value());
    }

    @Test
    void testUpdateActiveIntegrationCount() {
        // Arrange
        long count = 5;

        // Act
        hipMetricsService.updateActiveIntegrationCount(count);

        // Assert
        Gauge gauge = meterRegistry.find("hip.integrations.active.count").gauge();
        assertNotNull(gauge);
        assertEquals(count, gauge.value());
    }

    @Test
    void testUpdateActiveThrottleCount() {
        // Arrange
        long count = 3;

        // Act
        hipMetricsService.updateActiveThrottleCount(count);

        // Assert
        Gauge gauge = meterRegistry.find("hip.throttle.active.count").gauge();
        assertNotNull(gauge);
        assertEquals(count, gauge.value());
    }

    @Test
    void testRecordAdapterMetrics() {
        // Arrange
        String adapterType = "kafka";
        String operation = "send";
        long duration = 100;
        String status = "success";

        // Act
        hipMetricsService.recordAdapterMetrics(adapterType, operation, duration, status);

        // Assert
        Timer timer = meterRegistry.find("hip.adapter.operation.duration")
                .tag("adapter_type", adapterType)
                .tag("operation", operation)
                .tag("status", status)
                .timer();
        
        assertNotNull(timer);
        assertEquals(1, timer.count());
        assertEquals(duration, timer.totalTime(java.util.concurrent.TimeUnit.MILLISECONDS), 1.0);
    }

    @Test
    void testMultipleQueueDepthUpdates() {
        // Arrange
        String queueName = "test-queue";

        // Act
        hipMetricsService.updateQueueDepth(queueName, 10);
        hipMetricsService.updateQueueDepth(queueName, 20);
        hipMetricsService.updateQueueDepth(queueName, 15);

        // Assert
        Gauge gauge = meterRegistry.find("hip.queue.depth." + queueName)
                .tag("queue", queueName)
                .gauge();
        
        assertNotNull(gauge);
        assertEquals(15, gauge.value()); // Should have the latest value
    }

    @Test
    void testMetricsWithDifferentTags() {
        // Test that metrics with different tags are tracked separately
        hipMetricsService.incrementMessageThroughput("integration1", "1.0", "http");
        hipMetricsService.incrementMessageThroughput("integration1", "1.0", "kafka");
        hipMetricsService.incrementMessageThroughput("integration2", "1.0", "http");

        // Assert separate counters
        Counter httpCounter1 = meterRegistry.find("hip.message.throughput")
                .tag("integration", "integration1")
                .tag("version", "1.0")
                .tag("adapter", "http")
                .counter();
        
        Counter kafkaCounter1 = meterRegistry.find("hip.message.throughput")
                .tag("integration", "integration1")
                .tag("version", "1.0")
                .tag("adapter", "kafka")
                .counter();
        
        Counter httpCounter2 = meterRegistry.find("hip.message.throughput")
                .tag("integration", "integration2")
                .tag("version", "1.0")
                .tag("adapter", "http")
                .counter();

        assertNotNull(httpCounter1);
        assertNotNull(kafkaCounter1);
        assertNotNull(httpCounter2);
        
        assertEquals(1.0, httpCounter1.count());
        assertEquals(1.0, kafkaCounter1.count());
        assertEquals(1.0, httpCounter2.count());
    }
}
