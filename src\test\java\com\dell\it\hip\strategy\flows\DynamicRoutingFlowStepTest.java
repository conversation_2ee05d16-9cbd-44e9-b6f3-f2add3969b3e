package com.dell.it.hip.strategy.flows;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.GenericMessage;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.routing.RoutingDecision;
import com.dell.it.hip.util.routing.RoutingRuleEngine;

/**
 * Unit tests for DynamicRoutingFlowStep.
 */
@ExtendWith(MockitoExtension.class)
class DynamicRoutingFlowStepTest {

    @Mock
    private RoutingRuleEngine ruleEngine;

    @Mock
    private WiretapService wiretapService;

    @Mock
    private TransactionLoggingUtil transactionLoggingUtil;

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private MessageChannel messageChannel;

    @Mock
    private HandlerStrategy handlerStrategy;

    @Mock
    private HIPIntegrationDefinition integrationDefinition;

    @InjectMocks
    private DynamicRoutingFlowStep dynamicRoutingFlowStep;

    private Message<?> testMessage;
    private FlowStepConfigRef stepConfigRef;
    private FlowStepConfig stepConfig;

    @BeforeEach
    void setUp() {
        testMessage = new GenericMessage<>("test payload");

        stepConfigRef = new FlowStepConfigRef();
        stepConfigRef.setPropertyRef("test.routing.config");

        // Setup mock behavior for integrationDefinition (lenient to avoid unnecessary stubbing warnings)
        lenient().when(integrationDefinition.getHipIntegrationName()).thenReturn("test-integration");
        lenient().when(integrationDefinition.getVersion()).thenReturn("1.0");

        stepConfig = new FlowStepConfig();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("ruleKey", "test-rule");
        stepConfig.setParameters(parameters);
    }

    @Test
    void testGetType() {
        assertEquals("dynamicRouter", dynamicRoutingFlowStep.getType());
    }

    @Test
    void testDoExecute_ChannelRouting_Success() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        RoutingDecision decision = RoutingDecision.forChannel("test-channel");
        when(ruleEngine.evaluate("test-rule", testMessage, stepConfig, integrationDefinition))
                .thenReturn(decision);

        when(serviceManager.getChannels("test-integration", "1.0"))
                .thenReturn(List.of(messageChannel));

        when(messageChannel.send(testMessage)).thenReturn(true);

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(messageChannel).send(testMessage);
        verify(ruleEngine).evaluate("test-rule", testMessage, stepConfig, integrationDefinition);
    }

    @Test
    void testDoExecute_HandlerRouting_Success() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        HandlerConfigRef handlerRef = new HandlerConfigRef();
        handlerRef.setType("test-handler");

        RoutingDecision decision = RoutingDecision.forHandler(handlerRef);
        when(ruleEngine.evaluate("test-rule", testMessage, stepConfig, integrationDefinition))
                .thenReturn(decision);

        when(serviceManager.getHandlerStrategy("test-handler")).thenReturn(handlerStrategy);

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(handlerStrategy).handle(testMessage, integrationDefinition, handlerRef);
        verify(ruleEngine).evaluate("test-rule", testMessage, stepConfig, integrationDefinition);
    }

    @Test
    void testDoExecute_NoStepConfig_ReturnsEmptyList() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(null);

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(ruleEngine, never()).evaluate(any(), any(), any(), any());
    }

    @Test
    void testDoExecute_NoRuleKey_ReturnsEmptyList() throws Exception {
        // Arrange
        stepConfig.setParameters(new HashMap<>());
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(ruleEngine, never()).evaluate(any(), any(), any(), any());
    }

    @Test
    void testDoExecute_NoRoutingDecision_ReturnsEmptyList() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        when(ruleEngine.evaluate("test-rule", testMessage, stepConfig, integrationDefinition))
                .thenReturn(RoutingDecision.none());

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(ruleEngine).evaluate("test-rule", testMessage, stepConfig, integrationDefinition);
    }

    @Test
    void testDoExecute_ChannelNotFound_ReturnsEmptyList() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        RoutingDecision decision = RoutingDecision.forChannel("test-channel");
        when(ruleEngine.evaluate("test-rule", testMessage, stepConfig, integrationDefinition))
                .thenReturn(decision);

        when(serviceManager.getChannels("test-integration", "1.0"))
                .thenReturn(Collections.emptyList());

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(messageChannel, never()).send(any());
    }

    @Test
    void testDoExecute_HandlerStrategyNotFound_ReturnsEmptyList() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        HandlerConfigRef handlerRef = new HandlerConfigRef();
        handlerRef.setType("unknown-handler");

        RoutingDecision decision = RoutingDecision.forHandler(handlerRef);
        when(ruleEngine.evaluate("test-rule", testMessage, stepConfig, integrationDefinition))
                .thenReturn(decision);

        when(serviceManager.getHandlerStrategy("unknown-handler")).thenReturn(null);

        // Act
        List<Message<?>> result = dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(handlerStrategy, never()).handle(any(), any(), any());
    }

    @Test
    void testDoExecute_ExceptionThrown_RethrowsException() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig("test.routing.config", FlowStepConfig.class))
                .thenReturn(stepConfig);

        when(ruleEngine.evaluate("test-rule", testMessage, stepConfig, integrationDefinition))
                .thenThrow(new RuntimeException("Test exception"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            dynamicRoutingFlowStep.doExecute(testMessage, stepConfigRef, integrationDefinition);
        });
    }
}
