package com.dell.it.hip.util.routing;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for RoutingRuleEntity.
 */
@Repository
public interface RoutingRuleRepository extends JpaRepository<RoutingRuleEntity, Long> {

    /**
     * Find all routing rules by rule key, ordered by priority (ascending).
     * Only returns enabled rules.
     */
    @Query("SELECT r FROM RoutingRuleEntity r WHERE r.ruleKey = :ruleKey AND r.enabled = true ORDER BY r.priority ASC, r.id ASC")
    List<RoutingRuleEntity> findByRuleKeyAndEnabledTrueOrderByPriorityAsc(@Param("ruleKey") String ruleKey);

    /**
     * Find all routing rules by rule key (including disabled ones).
     */
    List<RoutingRuleEntity> findByRuleKeyOrderByPriorityAsc(String ruleKey);

    /**
     * Find all enabled routing rules.
     */
    List<RoutingRuleEntity> findByEnabledTrueOrderByRuleKeyAscPriorityAsc();

    /**
     * Find routing rules by route type.
     */
    List<RoutingRuleEntity> findByRouteTypeAndEnabledTrueOrderByPriorityAsc(RoutingDecision.RouteType routeType);

    /**
     * Check if any routing rules exist for a given rule key.
     */
    boolean existsByRuleKeyAndEnabledTrue(String ruleKey);

    /**
     * Delete all routing rules for a given rule key.
     */
    void deleteByRuleKey(String ruleKey);
}
