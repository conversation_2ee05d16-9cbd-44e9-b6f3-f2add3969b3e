package com.dell.it.hip.config.adapters;

import lombok.Data;

import java.util.Map;

@Data
public class AdapterConfig {
    private String type; // e.g., "kafka", "rabbitmq", "ibmmq", "sftp"
    private String role; // "primary" or "fallback"
    private String configRef; // reference to property sheet or config key
    private Map<String,?> properties;
    // Additional adapter-specific fields can be added in subclasses



}