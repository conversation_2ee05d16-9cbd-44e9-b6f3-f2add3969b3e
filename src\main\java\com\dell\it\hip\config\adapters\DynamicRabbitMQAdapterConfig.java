package com.dell.it.hip.config.adapters;
import java.util.List;
import java.util.Map;

import org.springframework.amqp.core.AcknowledgeMode;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DynamicRabbitMQAdapterConfig extends AdapterConfig {

    // RabbitMQ connection settings
    private String host;
    private Integer port;
    private String virtualHost;
    private String queueName;

    // Authentication
    private String authenticationType; // "BASIC", "TLS", "OAUTH2", etc
    private String username;
    private String password;

    // TLS/SSL properties
    private String sslTruststoreLocation;
    private String sslTruststorePassword;
    private String sslKeystoreLocation;
    private String sslKeystorePassword;
    private String sslKeyPassword;

    // Consumer settings
    private Integer concurrency;
    private Integer prefetchCount;
    private AcknowledgeMode acknowledgeMode; // NONE, AUTO, MANUAL
    private Integer channelCacheSize; // For connection pooling

    // Compression
    private boolean compressed = false;

    // Header extraction
    private List<String> headersToExtract;

    // Custom/Advanced
    private Map<String, String> properties;

    // (optional) Message converter
    private String messageConverterClass;

    // --- <PERSON>ters and Setters ---

    // ...all getters and setters for above fields...

}