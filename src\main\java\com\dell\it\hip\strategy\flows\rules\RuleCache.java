package com.dell.it.hip.strategy.flows.rules;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class RuleCache {
    @Autowired private StringRedisTemplate redisTemplate;
    @Autowired private ObjectMapper objectMapper;

    // Use RuleRef as the cache key (name + version)
    private final Map<RuleRef, Rule> cache = new ConcurrentHashMap<>();

    public Rule getRule(String ruleName, String ruleVersion) {
        RuleRef ref = new RuleRef();
        ref.setRuleName(ruleName);
        ref.setRuleVersion(ruleVersion);
        return cache.computeIfAbsent(ref, this::loadRule);
    }

    public List<Rule> getRules(List<RuleRef> ruleRefs) {
        List<Rule> list = new ArrayList<>();
        for (RuleRef ref : ruleRefs) {
            Rule r = getRule(ref.getRuleName(), ref.getRuleVersion());
            if (r != null) list.add(r);
        }
        return list;
    }

    // Loads one rule from Redis and caches it
    public Rule loadRule(RuleRef ref) {
        String key = HIPRedisKeyUtil.ruleKey(ref.getRuleName(), ref.getRuleVersion());
        String json = redisTemplate.opsForValue().get(key);
        if (json == null) return null;
        try {
            return objectMapper.readValue(json, Rule.class);
        } catch (Exception e) {
            // log error
            return null;
        }
    }

    // Loads all rules by RuleRef (name + version) and caches in memory
    public void preloadRules(List<RuleRef> ruleRefs) {
        for (RuleRef ref : ruleRefs) {
            String key = HIPRedisKeyUtil.ruleKey(ref.getRuleName(), ref.getRuleVersion());
            String json = redisTemplate.opsForValue().get(key);
            if (json != null) {
                try {
                    Rule rule = objectMapper.readValue(json, Rule.class);
                    cache.put(ref, rule);
                } catch (Exception ex) {
                    // log error
                }
            }
        }
    }
}