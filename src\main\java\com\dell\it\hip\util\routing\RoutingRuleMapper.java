package com.dell.it.hip.util.routing;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

/**
 * Mapper to convert between RoutingRuleEntity and RoutingRule.
 */
@Component
public class RoutingRuleMapper {

    /**
     * Convert RoutingRuleEntity to RoutingRule.
     */
    public RoutingRule toRoutingRule(RoutingRuleEntity entity) {
        if (entity == null) {
            return null;
        }

        RoutingRule rule = new RoutingRule();
        rule.setRouteType(entity.getRouteType());
        rule.setChannelName(entity.getChannelName());
        rule.setHandlerConfigRefId(entity.getHandlerConfigRefId());
        rule.setCondition(entity.getCondition());
        
        return rule;
    }

    /**
     * Convert RoutingRule to RoutingRuleEntity.
     */
    public RoutingRuleEntity toRoutingRuleEntity(RoutingRule rule, String ruleKey) {
        if (rule == null) {
            return null;
        }

        RoutingRuleEntity entity = new RoutingRuleEntity();
        entity.setRuleKey(ruleKey);
        entity.setRouteType(rule.getRouteType());
        entity.setChannelName(rule.getChannelName());
        entity.setHandlerConfigRefId(rule.getHandlerConfigRefId());
        entity.setCondition(rule.getCondition());
        entity.setEnabled(true);
        entity.setPriority(0);
        
        return entity;
    }

    /**
     * Convert list of RoutingRuleEntity to list of RoutingRule.
     */
    public List<RoutingRule> toRoutingRules(List<RoutingRuleEntity> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(this::toRoutingRule)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of RoutingRule to list of RoutingRuleEntity.
     */
    public List<RoutingRuleEntity> toRoutingRuleEntities(List<RoutingRule> rules, String ruleKey) {
        if (rules == null) {
            return null;
        }

        return rules.stream()
                .map(rule -> toRoutingRuleEntity(rule, ruleKey))
                .collect(Collectors.toList());
    }
}
