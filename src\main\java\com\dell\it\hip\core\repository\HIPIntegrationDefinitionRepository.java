package com.dell.it.hip.core.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.dell.it.hip.config.HIPIntegrationDefinition;

@Repository
public interface HIPIntegrationDefinitionRepository extends JpaRepository<HIPIntegrationDefinition, String> {
    List<HIPIntegrationDefinition> findByServiceManagerName(String serviceManagerName);

    boolean existsByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version);

    HIPIntegrationDefinition findByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version);
}
