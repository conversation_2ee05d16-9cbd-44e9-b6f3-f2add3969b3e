# Integration Testing with TestContainers

This document provides comprehensive guidance for running integration tests in the HIP Services project using TestContainers.

## Prerequisites

### Required Software
- **Java 17+** - Required for running the application
- **Maven 3.8+** - Build tool
- **Docker Desktop** - Required for TestContainers
  - Windows: [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
  - macOS: [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
  - Linux: [Docker Engine](https://docs.docker.com/engine/install/)

### Docker Configuration
1. **Install Docker Desktop** and ensure it's running
2. **Verify Docker is accessible** from command line:
   ```bash
   docker --version
   docker ps
   ```
3. **Ensure sufficient resources** are allocated to Docker:
   - Memory: At least 4GB (8GB recommended)
   - CPU: At least 2 cores
   - Disk: At least 10GB free space

## Docker Environment Validation

Before running integration tests, validate your Docker environment:

### Windows (PowerShell)
```powershell
.\scripts\validate-docker.ps1
```

### Linux/macOS (Bash)
```bash
chmod +x scripts/validate-docker.sh
./scripts/validate-docker.sh
```

## Integration Test Architecture

### Base Test Configuration
All integration tests extend `BaseIntegrationTest` which provides:
- **Shared TestContainers setup** for PostgreSQL, Redis, and Kafka
- **Docker environment validation** before test execution
- **Automatic container lifecycle management**
- **Dynamic property configuration** for Spring Boot

### Available Test Containers
- **PostgreSQL 15-alpine** - Database operations testing
- **Redis 6.2.6** - Caching and pub/sub functionality testing
- **Kafka (Confluent CP 7.4.0)** - Message broker testing

### Test Classes
1. **RedisIntegrationTest** - Tests Redis connectivity and operations
2. **MessageFlowIntegrationTest** - End-to-end message flow testing

## Running Integration Tests

### Individual Test Classes
```bash
# Run Redis integration tests
mvn test -Dtest=RedisIntegrationTest

# Run Message Flow integration tests
mvn test -Dtest=MessageFlowIntegrationTest
```

### All Integration Tests
```bash
# Run all integration tests using Failsafe plugin
mvn verify

# Run all tests (unit + integration)
mvn clean verify
```

### With Specific Profiles
```bash
# Run with test profile
mvn test -Dspring.profiles.active=test -Dtest=RedisIntegrationTest
```

## Test Configuration

### TestContainers Configuration
Configuration is managed through `src/test/resources/testcontainers.properties`:
- Container reuse enabled for faster test execution
- Startup timeout: 120 seconds
- Automatic cleanup enabled

### Spring Test Configuration
Test-specific configuration in `src/test/resources/application-test.yml`:
- H2 in-memory database for unit tests
- TestContainers override database/Redis/Kafka connections
- Debug logging enabled for troubleshooting

## Troubleshooting

### Common Issues and Solutions

#### 1. "Could not find a valid Docker environment"
**Cause**: Docker is not running or not accessible
**Solution**:
- Ensure Docker Desktop is running
- Run the validation script: `.\scripts\validate-docker.ps1`
- Check Docker daemon status: `docker info`

#### 2. "Container startup timeout"
**Cause**: Containers taking too long to start
**Solution**:
- Increase Docker resource allocation (memory/CPU)
- Check network connectivity for image pulls
- Increase timeout in `testcontainers.properties`

#### 3. "Port already in use"
**Cause**: Another service using the same ports
**Solution**:
- Stop conflicting services
- TestContainers automatically assigns random ports
- Check for running containers: `docker ps`

#### 4. "Image pull failures"
**Cause**: Network connectivity or Docker Hub access issues
**Solution**:
- Check internet connectivity
- Configure corporate proxy if needed
- Use alternative image registries

### Debug Mode
Enable debug logging for detailed troubleshooting:
```bash
mvn test -Dtest=RedisIntegrationTest -Dlogging.level.org.testcontainers=DEBUG
```

### Container Logs
View container logs during test execution:
```java
// In test methods
System.out.println("Redis logs: " + redis.getLogs());
```

## Performance Optimization

### Container Reuse
TestContainers reuse is enabled to improve test performance:
- Containers are reused across test runs
- Significantly reduces startup time
- Containers are cleaned up when JVM exits

### Resource Management
- **Memory**: Containers use minimal resources
- **Startup Time**: ~30-60 seconds for all containers
- **Cleanup**: Automatic via Ryuk container

## CI/CD Integration

### GitHub Actions / Jenkins
```yaml
# Example GitHub Actions configuration
- name: Start Docker
  run: |
    sudo systemctl start docker
    docker --version

- name: Run Integration Tests
  run: mvn verify
```

### Docker-in-Docker
For containerized CI environments:
```yaml
services:
  docker:
    image: docker:dind
    privileged: true
```

## Best Practices

### Test Design
1. **Extend BaseIntegrationTest** for shared setup
2. **Use @DynamicPropertySource** for container configuration
3. **Clean up test data** in @BeforeEach methods
4. **Use meaningful test names** describing scenarios

### Container Management
1. **Reuse containers** when possible
2. **Use specific image versions** for reproducibility
3. **Configure appropriate timeouts**
4. **Monitor resource usage**

### Error Handling
1. **Validate Docker environment** before tests
2. **Provide clear error messages**
3. **Log container information** for debugging
4. **Graceful degradation** when Docker unavailable

## Additional Resources

- [TestContainers Documentation](https://www.testcontainers.org/)
- [Spring Boot Testing Guide](https://spring.io/guides/gs/testing-web/)
- [Docker Desktop Documentation](https://docs.docker.com/desktop/)
- [Maven Failsafe Plugin](https://maven.apache.org/surefire/maven-failsafe-plugin/)

## Support

For issues with integration tests:
1. Run the Docker validation script
2. Check the troubleshooting section
3. Review container logs
4. Contact the development team with detailed error messages
