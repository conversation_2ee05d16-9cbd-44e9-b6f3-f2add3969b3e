package com.dell.it.hip.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.dell.it.hip.util.redis.HIPRedisKeyUtil;

/**
 * Unit tests for HIPRedisKeyUtil.
 */
class HIPRedisKeyUtilTest {

    private static final String SERVICE_MANAGER = "test-service-manager";
    private static final String INTEGRATION_NAME = "test-integration";
    private static final String VERSION = "1.0";
    private static final String ADAPTER_ID = "adapter-123";

    @Test
    void testThrottleKey_FourParameters() {
        String result = HIPRedisKeyUtil.throttleKey(SERVICE_MANAGER, INTEGRATION_NAME, VERSION, ADAPTER_ID);
        
        assertNotNull(result);
        assertTrue(result.contains(SERVICE_MANAGER));
        assertTrue(result.contains(INTEGRATION_NAME));
        assertTrue(result.contains(VERSION));
        assertTrue(result.contains(ADAPTER_ID));
        assertTrue(result.contains("throttle"));
        assertEquals("hip:" + SERVICE_MANAGER + ":adapter:throttle:" + INTEGRATION_NAME + ":" + VERSION + ":" + ADAPTER_ID, result);
    }

    @Test
    void testThrottleKey_ThreeParameters() {
        String result = HIPRedisKeyUtil.throttleKey(SERVICE_MANAGER, INTEGRATION_NAME, VERSION,ADAPTER_ID);
        
        assertNotNull(result);
        assertTrue(result.contains(SERVICE_MANAGER));
        assertTrue(result.contains(INTEGRATION_NAME));
        assertTrue(result.contains(VERSION));
        assertTrue(result.contains("throttle"));
        assertEquals("hip:" + SERVICE_MANAGER + ":integration:throttle:" + INTEGRATION_NAME + ":" + VERSION, result);
    }

    @Test
    void testKeyGeneration_WithNullValues() {
        // Test behavior with null service manager name
        assertThrows(NullPointerException.class, () -> {
            HIPRedisKeyUtil.throttleKey(null, INTEGRATION_NAME, VERSION, ADAPTER_ID);
        });
    }

    @Test
    void testKeyGeneration_WithEmptyValues() {
        String result = HIPRedisKeyUtil.throttleKey("", INTEGRATION_NAME, VERSION, ADAPTER_ID);
        
        assertNotNull(result);
        assertTrue(result.startsWith("hip::"));
    }

    @Test
    void testKeyGeneration_WithSpecialCharacters() {
        String serviceManagerWithSpecialChars = "service-manager_test.123";
        String integrationWithSpecialChars = "integration-name_test.456";
        
        String result = HIPRedisKeyUtil.throttleKey(serviceManagerWithSpecialChars, integrationWithSpecialChars, VERSION, ADAPTER_ID);
        
        assertNotNull(result);
        assertTrue(result.contains(serviceManagerWithSpecialChars));
        assertTrue(result.contains(integrationWithSpecialChars));
    }

    @Test
    void testAllMethodsReturnNonEmptyStrings() {
        // Test that all methods return non-empty strings with valid inputs
        assertFalse(HIPRedisKeyUtil.throttleKey(SERVICE_MANAGER, INTEGRATION_NAME, VERSION, ADAPTER_ID).isEmpty());
        assertFalse(HIPRedisKeyUtil.throttleKey(SERVICE_MANAGER, INTEGRATION_NAME, VERSION,ADAPTER_ID).isEmpty());
        
    }

    @Test
    void testKeyUniqueness() {
        // Test that different parameters produce different keys
        String key1 = HIPRedisKeyUtil.throttleKey("service1", "integration1", "1.0", "adapter1");
        String key2 = HIPRedisKeyUtil.throttleKey("service2", "integration1", "1.0", "adapter1");
        String key3 = HIPRedisKeyUtil.throttleKey("service1", "integration2", "1.0", "adapter1");
        String key4 = HIPRedisKeyUtil.throttleKey("service1", "integration1", "2.0", "adapter1");
        String key5 = HIPRedisKeyUtil.throttleKey("service1", "integration1", "1.0", "adapter2");
        
        assertNotEquals(key1, key2);
        assertNotEquals(key1, key3);
        assertNotEquals(key1, key4);
        assertNotEquals(key1, key5);
    }
}
