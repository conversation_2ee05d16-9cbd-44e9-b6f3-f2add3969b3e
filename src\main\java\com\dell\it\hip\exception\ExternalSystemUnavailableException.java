package com.dell.it.hip.exception;

/**
 * Exception thrown when an external system is unavailable.
 */
public class ExternalSystemUnavailableException extends RuntimeException {
    
    private final String systemName;
    private final String endpoint;
    
    public ExternalSystemUnavailableException(String message, String systemName, String endpoint) {
        super(message);
        this.systemName = systemName;
        this.endpoint = endpoint;
    }
    
    public ExternalSystemUnavailableException(String message, String systemName, String endpoint, Throwable cause) {
        super(message, cause);
        this.systemName = systemName;
        this.endpoint = endpoint;
    }
    
    public ExternalSystemUnavailableException(String systemName, String endpoint, Throwable cause) {
        super(String.format("External system '%s' is unavailable at endpoint '%s': %s", 
                          systemName, endpoint, cause.getMessage()), cause);
        this.systemName = systemName;
        this.endpoint = endpoint;
    }
    
    public String getSystemName() {
        return systemName;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
}
