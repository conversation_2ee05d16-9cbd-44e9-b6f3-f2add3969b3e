package com.dell.it.hip.config.flowSteps;

import java.util.List;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;

import lombok.Data;
@Data
public class FlowRoutingConfig extends FlowStepConfig implements RuleEnabledStepConfig {
    private String documentType;
    private String flowIdentifier;
    private List<RuleRef> ruleRefs; // Use different field name to avoid conflict

    // Standard getters/setters
    public String getDocumentType() { return documentType; }
    public void setDocumentType(String documentType) { this.documentType = documentType; }

    public String getFlowIdentifier() { return flowIdentifier; }
    public void setFlowIdentifier(String flowIdentifier) { this.flowIdentifier = flowIdentifier; }

    // Implementation of RuleEnabledStepConfig interface
    @Override
    public List<RuleRef> getRuleRefs() {
        return ruleRefs;
    }

    public void setRuleRefs(List<RuleRef> ruleRefs) {
        this.ruleRefs = ruleRefs;
    }

    // Override parent's rules methods to maintain compatibility
    @Override
    public void setRules(List<String> rules) {
        // Convert String rules to RuleRef if needed for backward compatibility
        if (rules != null) {
            this.ruleRefs = rules.stream()
                .map(ruleName -> {
                    RuleRef ref = new RuleRef();
                    ref.setRuleName(ruleName);
                    ref.setRuleVersion("1.0"); // Default version
                    return ref;
                })
                .collect(java.util.stream.Collectors.toList());
        }
        super.setRules(rules);
    }

    // Provide access to parent's string-based rules for backward compatibility
    public List<String> getStringRules() {
        if (ruleRefs == null) {
            return super.getRules();
        }
        return ruleRefs.stream()
            .map(RuleRef::getRuleName)
            .collect(java.util.stream.Collectors.toList());
    }
}