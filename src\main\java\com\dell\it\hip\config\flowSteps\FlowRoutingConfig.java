package com.dell.it.hip.config.flowSteps;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;

import lombok.Data;

import org.springframework.context.annotation.Bean;

import java.util.List;
@Data
public class FlowRoutingConfig extends FlowStepConfig implements RuleEnabledStepConfig {
    private String documentType;
    private String flowIdentifier;
    private List<RuleRef> rules;

    // Standard getters/setters
    public String getDocumentType() { return documentType; }
    public void setDocumentType(String documentType) { this.documentType = documentType; }

    public String getFlowIdentifier() { return flowIdentifier; }
    public void setFlowIdentifier(String flowIdentifier) { this.flowIdentifier = flowIdentifier; }
}