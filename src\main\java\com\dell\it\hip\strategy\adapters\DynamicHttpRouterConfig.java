package com.dell.it.hip.strategy.adapters;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

import java.util.Map;

@Configuration
public class DynamicHttpRouterConfig {

    @Autowired
    private DynamicHttpsInputAdapter dynamicHttpsInputAdapter;

    @Bean
    public RouterFunction<ServerResponse> dynamicHttpRoutes() {
        RouterFunctions.Builder builder = RouterFunctions.route();

        // Add dynamic routes from the adapter
        dynamicHttpsInputAdapter.getHandlerMap().forEach((adapterKey, handler) -> {
            String path = "/hip/https/" + adapterKey;
            builder.POST(path, handler);
        });

        // Add a default fallback route to ensure at least one route exists
        // This prevents the "No routes registered" error during application startup
        builder.GET("/hip/https/health", request ->
            ServerResponse.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(Map.of(
                    "status", "ok",
                    "message", "HIP HTTPS adapter is running",
                    "registeredRoutes", dynamicHttpsInputAdapter.getHandlerMap().size()
                ))
        );

        return builder.build();
    }
}