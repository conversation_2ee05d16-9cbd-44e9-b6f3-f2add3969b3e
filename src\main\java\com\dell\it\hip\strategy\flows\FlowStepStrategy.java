package com.dell.it.hip.strategy.flows;

import com.dell.it.hip.config.flowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import org.springframework.messaging.Message;

import java.util.List;

/**
 * Strategy for individual steps in the flow (validation, enrichment, routing, etc).
 */
public interface FlowStepStrategy {
    String getType();

    // Build and pre-wire the step; can be a no-op if not needed
    default void buildStep(HIPIntegrationDefinition def, FlowStepConfigRef ref) {}

    // At runtime, execute the step with resolved config

    List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) throws Exception;
}