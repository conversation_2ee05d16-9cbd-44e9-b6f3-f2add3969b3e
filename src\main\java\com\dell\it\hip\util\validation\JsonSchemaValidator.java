package com.dell.it.hip.util.validation;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Set;

import org.json.JSONObject;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;

import lombok.extern.slf4j.Slf4j;
@Slf4j
public class JsonSchemaValidator {

    public static boolean isWellFormed(String json) {
        try {
            new JSONObject(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

	public static boolean validate(String json, String schemaContent) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			JsonNode schemaNode = objectMapper.readTree(schemaContent);
			JsonSchemaFactory factory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012); // Or V4/V7/V201909, as needed
			JsonSchema schema = factory.getSchema(schemaNode);

			JsonNode jsonNode = objectMapper.readTree(json);
			Set<ValidationMessage> errors = schema.validate(jsonNode);
			return errors == null || errors.isEmpty();
		} catch (Exception e) {
			return false;
		}
	}
}