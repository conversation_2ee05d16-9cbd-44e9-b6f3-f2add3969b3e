package com.dell.it.hip.exception;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

/**
 * Global exception handler for standardized error responses.
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(IntegrationNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleIntegrationNotFound(
            IntegrationNotFoundException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("Integration not found [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.NOT_FOUND.value(),
            "INTEGRATION_NOT_FOUND",
            ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(AdapterConfigurationException.class)
    public ResponseEntity<ErrorResponse> handleAdapterConfiguration(
            AdapterConfigurationException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("Adapter configuration error [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.BAD_REQUEST.value(),
            "ADAPTER_CONFIGURATION_ERROR",
            ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ThrottleLimitExceededException.class)
    public ResponseEntity<ErrorResponse> handleThrottleLimitExceeded(
            ThrottleLimitExceededException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.warn("Throttle limit exceeded [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.TOO_MANY_REQUESTS.value(),
            "THROTTLE_LIMIT_EXCEEDED",
            ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.TOO_MANY_REQUESTS);
    }

    @ExceptionHandler(ExternalSystemUnavailableException.class)
    public ResponseEntity<ErrorResponse> handleExternalSystemUnavailable(
            ExternalSystemUnavailableException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("External system unavailable [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.SERVICE_UNAVAILABLE.value(),
            "EXTERNAL_SYSTEM_UNAVAILABLE",
            ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.SERVICE_UNAVAILABLE);
    }

    @ExceptionHandler(MessageTransformationException.class)
    public ResponseEntity<ErrorResponse> handleMessageTransformation(
            MessageTransformationException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("Message transformation error [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.UNPROCESSABLE_ENTITY.value(),
            "MESSAGE_TRANSFORMATION_ERROR",
            ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @ExceptionHandler(RoutingDecisionException.class)
    public ResponseEntity<ErrorResponse> handleRoutingDecision(
            RoutingDecisionException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("Routing decision error [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.BAD_REQUEST.value(),
            "ROUTING_DECISION_ERROR",
            ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("Validation error [{}]: {}", correlationId, ex.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.BAD_REQUEST.value(),
            "VALIDATION_ERROR",
            "Validation failed for request fields",
            request.getDescription(false),
            correlationId
        );
        errorResponse.setValidationErrors(errors);
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponse> handleAccessDenied(
            AccessDeniedException ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.warn("Access denied [{}]: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.FORBIDDEN.value(),
            "ACCESS_DENIED",
            "Access denied: " + ex.getMessage(),
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, WebRequest request) {
        
        String correlationId = generateCorrelationId();
        logger.error("Unexpected error [{}]: {}", correlationId, ex.getMessage(), ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
            HttpStatus.INTERNAL_SERVER_ERROR.value(),
            "INTERNAL_SERVER_ERROR",
            "An unexpected error occurred. Please contact support with correlation ID: " + correlationId,
            request.getDescription(false),
            correlationId
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private String generateCorrelationId() {
        return UUID.randomUUID().toString();
    }

    /**
     * Standardized error response format.
     */
    public static class ErrorResponse {
        private int status;
        private String errorCode;
        private String message;
        private String path;
        private String correlationId;
        private String timestamp;
        private Map<String, String> validationErrors;

        public ErrorResponse(int status, String errorCode, String message, String path, String correlationId) {
            this.status = status;
            this.errorCode = errorCode;
            this.message = message;
            this.path = path;
            this.correlationId = correlationId;
            this.timestamp = Instant.now().toString();
        }

        // Getters and setters
        public int getStatus() { return status; }
        public void setStatus(int status) { this.status = status; }
        
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public String getCorrelationId() { return correlationId; }
        public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }
        
        public String getTimestamp() { return timestamp; }
        public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
        
        public Map<String, String> getValidationErrors() { return validationErrors; }
        public void setValidationErrors(Map<String, String> validationErrors) { this.validationErrors = validationErrors; }
    }
}
