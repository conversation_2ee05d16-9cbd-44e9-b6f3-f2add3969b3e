package com.dell.it.hip.strategy.flows.rules;

import com.dell.it.hip.strategy.flows.rules.RuleAction;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class FlowResponseAction implements RuleAction {

    @Override
    public String getName() { return "flowResponse"; }

    @Override
    public String getType() { return "FlowResponse"; }

    @Override
    public Message<?> performAction(Message<?> msg, Map<String, Object> params) {
        List<String> channels = new ArrayList<>();
        Object chObj = params.get("channels");
        if (chObj instanceof String) {
            channels = Arrays.asList(((String) chObj).split(","));
        } else if (chObj instanceof List) {
            channels = (List<String>) chObj;
        } else {
            String c = (String) params.get("channel");
            if (c != null) channels.add(c);
        }

        // Set header with all target channels (no actual send)
        return MessageBuilder.fromMessage(msg)
                .setHeader("HIP.targetFlowChannels", channels)
                .build();
    }
}