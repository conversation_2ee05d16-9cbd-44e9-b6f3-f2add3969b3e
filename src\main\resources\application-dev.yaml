server:
  port: 8081

spring:
  main:
    allow-circular-references: true
  datasource:
    url: jdbc:h2:mem:devdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  data:
    redis:
      host: localhost
      port: 6370
      embedded:
        enabled: true
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false
  config:
    import: "optional:configserver:"

service:
  manager:
    name: DevIntegrationManager
  concurrency:
    corePoolSize: 2
    maxPoolSize: 4
    queueCapacity: 100

hip:
  kafka:
    bootstrap-servers: "localhost:9092"
    security-protocol: PLAINTEXT

# Disable external dependencies for development
management:
  health:
    redis:
      enabled: true
    db:
      enabled: true

# Logging configuration for development
logging:
  level:
    root: info
    com.dell.it.hip: debug
    org.springframework.data.redis: debug
    redis.embedded: info
