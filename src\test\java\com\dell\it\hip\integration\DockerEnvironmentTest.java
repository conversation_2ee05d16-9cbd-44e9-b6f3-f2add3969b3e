package com.dell.it.hip.integration;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assumptions.assumeTrue;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.DockerClientFactory;

/**
 * Test to validate Docker environment for TestContainers.
 * This test verifies that Docker is available and accessible.
 */
class DockerEnvironmentTest {

    private static final Logger logger = LoggerFactory.getLogger(DockerEnvironmentTest.class);

    @Test
    void testDockerEnvironmentAvailable() {
        logger.info("Testing Docker environment availability...");
        
        try {
            boolean dockerAvailable = DockerClientFactory.instance().isDockerAvailable();
            
            if (!dockerAvailable) {
                logger.warn("Docker is not available. This test will be skipped.");
                logger.warn("To run integration tests, please:");
                logger.warn("1. Install Docker Desktop");
                logger.warn("2. Start Docker Desktop");
                logger.warn("3. Ensure Docker daemon is running");
                logger.warn("4. Run the validation script: .\\scripts\\validate-docker.ps1");
            }
            
            // Skip test if Docker is not available instead of failing
            assumeTrue(dockerAvailable, "Docker environment is not available");
            
            // If we reach here, Docker is available
            assertTrue(dockerAvailable, "Docker should be available");
            logger.info("✓ Docker environment is available and ready for TestContainers");
            
        } catch (Exception e) {
            logger.error("Failed to check Docker environment: {}", e.getMessage());
            logger.warn("This might indicate:");
            logger.warn("- Docker Desktop is not installed");
            logger.warn("- Docker daemon is not running");
            logger.warn("- Docker is not accessible from the current user");
            
            // Skip test instead of failing
            assumeTrue(false, "Docker environment check failed: " + e.getMessage());
        }
    }

    @Test
    void testDockerClientFactory() {
        logger.info("Testing DockerClientFactory initialization...");
        
        try {
            // This should not throw an exception even if Docker is not available
            DockerClientFactory factory = DockerClientFactory.instance();
            assertTrue(factory != null, "DockerClientFactory should be initialized");
            logger.info("✓ DockerClientFactory initialized successfully");
            
        } catch (Exception e) {
            logger.error("DockerClientFactory initialization failed: {}", e.getMessage());
            // This test should not fail even if Docker is not available
            // It's testing the factory initialization, not Docker availability
            assertTrue(false, "DockerClientFactory should initialize even without Docker");
        }
    }
}
