package com.dell.it.hip.core.registry;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.dell.it.hip.config.adapters.DynamicIBMMQAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicRabbitMQAdapterConfig;

@Component
public class ConfigClassRegistry {

    private final Map<String, Class<?>> adapterConfigClasses = new HashMap<>();
    private final Map<String, Class<?>> handlerConfigClasses = new HashMap<>();
    private final Map<String, Class<?>> stepConfigClasses = new HashMap<>();

    public ConfigClassRegistry() {
        // Register adapter config classes
        registerAdapter("kafka", DynamicKafkaAdapterConfig.class);
        registerAdapter("ibmmq", DynamicIBMMQAdapterConfig.class);
        registerAdapter("rabbitmq", DynamicRabbitMQAdapterConfig.class);
       // registerAdapter("sftp", SftpAdapterConfig.class);
        // ... add more as needed

        // Register handler config classes (can extend if needed)
        //registerHandler("kafka", KafkaAdapterConfig.class);
        //registerHandler("ibmmq", IbmMqAdapterConfig.class);
        //registerHandler("rabbitmq", RabbitMqAdapterConfig.class);
        //registerHandler("sftp", SftpAdapterConfig.class);

        // Register step config classes
        //registerStep("validation", ValidationStepConfig.class);
        //registerStep("headerEnricher", HeaderEnricherStepConfig.class);
        // ... add more as needed
    }

    public void registerAdapter(String type, Class<?> configClass) {
        adapterConfigClasses.put(type, configClass);
    }

    public void registerHandler(String type, Class<?> configClass) {
        handlerConfigClasses.put(type, configClass);
    }

    public void registerStep(String type, Class<?> configClass) {
        stepConfigClasses.put(type, configClass);
    }

    public Class<?> resolveAdapterConfigClass(String type) {
        Class<?> cls = adapterConfigClasses.get(type);
        if (cls == null) throw new IllegalArgumentException("Unknown adapter type: " + type);
        return cls;
    }

    public Class<?> resolveHandlerConfigClass(String type) {
        Class<?> cls = handlerConfigClasses.get(type);
        if (cls == null) throw new IllegalArgumentException("Unknown handler type: " + type);
        return cls;
    }

    public Class<?> resolveStepConfigClass(String type) {
        Class<?> cls = stepConfigClasses.get(type);
        if (cls == null) throw new IllegalArgumentException("Unknown step type: " + type);
        return cls;
    }
}