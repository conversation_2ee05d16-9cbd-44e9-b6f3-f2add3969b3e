package com.dell.it.hip.core.registry;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.dell.it.hip.config.Handlers.DynamicHttpsHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicIbmmqHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicKafkaHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicNasHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicRabbitMQHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicSftpHandlerConfig;
import com.dell.it.hip.config.adapters.DynamicHttpsAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicIBMMQAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicNASAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicRabbitMQAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicSFTPAdapterConfig;
import com.dell.it.hip.config.flowSteps.AttributeProcessorConfig;
import com.dell.it.hip.config.flowSteps.EnvelopeFlowStepConfig;
import com.dell.it.hip.config.flowSteps.FlowRoutingConfig;
import com.dell.it.hip.config.flowSteps.SplitterFlowStepConfig;
import com.dell.it.hip.config.flowSteps.ValidationFlowStepConfig;

@Component
public class ConfigClassRegistry {

    private final Map<String, Class<?>> adapterConfigClasses = new HashMap<>();
    private final Map<String, Class<?>> handlerConfigClasses = new HashMap<>();
    private final Map<String, Class<?>> stepConfigClasses = new HashMap<>();

    public ConfigClassRegistry() {
        // Register adapter config classes
        registerAdapter("kafka", DynamicKafkaAdapterConfig.class);
        registerAdapter("ibmmq", DynamicIBMMQAdapterConfig.class);
        registerAdapter("rabbitmq", DynamicRabbitMQAdapterConfig.class);
        registerAdapter("sftp", DynamicSFTPAdapterConfig.class);
        registerAdapter("nas", DynamicNASAdapterConfig.class);
        registerAdapter("https", DynamicHttpsAdapterConfig.class);
        // ... add more as needed

        // Register handler config classes (can extend if needed)
        registerHandler("kafka", DynamicKafkaHandlerConfig.class);
        registerHandler("ibmmq", DynamicIbmmqHandlerConfig.class);
        registerHandler("rabbitmq", DynamicRabbitMQHandlerConfig.class);
        registerHandler("sftp", DynamicSftpHandlerConfig.class);
        registerHandler("nas", DynamicNasHandlerConfig.class);
        registerHandler("https", DynamicHttpsHandlerConfig.class);

        // Register step config classes
        registerStep("validation", ValidationFlowStepConfig.class);
        registerStep("attributeProcessor", AttributeProcessorConfig.class);
        registerStep("splitter", SplitterFlowStepConfig.class);
        registerStep("envelope", EnvelopeFlowStepConfig.class);
        registerStep("flowRouting", FlowRoutingConfig.class);
    }

    public void registerAdapter(String type, Class<?> configClass) {
        adapterConfigClasses.put(type, configClass);
    }

    public void registerHandler(String type, Class<?> configClass) {
        handlerConfigClasses.put(type, configClass);
    }

    public void registerStep(String type, Class<?> configClass) {
        stepConfigClasses.put(type, configClass);
    }

    public Class<?> resolveAdapterConfigClass(String type) {
        Class<?> cls = adapterConfigClasses.get(type);
        if (cls == null) throw new IllegalArgumentException("Unknown adapter type: " + type);
        return cls;
    }

    public Class<?> resolveHandlerConfigClass(String type) {
        Class<?> cls = handlerConfigClasses.get(type);
        if (cls == null) throw new IllegalArgumentException("Unknown handler type: " + type);
        return cls;
    }

    public Class<?> resolveStepConfigClass(String type) {
        Class<?> cls = stepConfigClasses.get(type);
        if (cls == null) throw new IllegalArgumentException("Unknown step type: " + type);
        return cls;
    }
}