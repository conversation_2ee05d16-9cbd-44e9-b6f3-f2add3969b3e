package com.dell.it.hip.exception;

/**
 * Exception thrown when throttle limits are exceeded.
 */
public class ThrottleLimitExceededException extends RuntimeException {
    
    private final int currentCount;
    private final int maxAllowed;
    private final long retryAfterSeconds;
    
    public ThrottleLimitExceededException(String message, int currentCount, int maxAllowed, long retryAfterSeconds) {
        super(message);
        this.currentCount = currentCount;
        this.maxAllowed = maxAllowed;
        this.retryAfterSeconds = retryAfterSeconds;
    }
    
    public ThrottleLimitExceededException(String integrationName, String version, 
                                        int currentCount, int maxAllowed, long retryAfterSeconds) {
        super(String.format("Throttle limit exceeded for %s:%s. Current: %d, Max: %d. Retry after %d seconds",
                          integrationName, version, currentCount, maxAllowed, retryAfterSeconds));
        this.currentCount = currentCount;
        this.maxAllowed = maxAllowed;
        this.retryAfterSeconds = retryAfterSeconds;
    }
    
    public int getCurrentCount() {
        return currentCount;
    }
    
    public int getMaxAllowed() {
        return maxAllowed;
    }
    
    public long getRetryAfterSeconds() {
        return retryAfterSeconds;
    }
}
