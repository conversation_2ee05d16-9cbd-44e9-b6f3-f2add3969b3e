package com.dell.it.hip.config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.flowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.ThrottleSettings;

public class HIPIntegrationDefinition {

    private String hipIntegrationName;
    private String serviceManagerName;
    private String version;
    private String owner;
    private String businessFlowName;
    private String description;
    private String tags;

    private ThrottleSettings throttleSettings;



    // Config ref lists
    private List<AdapterConfigRef> adapterConfigRefs = new ArrayList<>();
    private List<HandlerConfigRef> handlerConfigRefs = new ArrayList<>();
    private List<FlowStepConfigRef> flowStepConfigRefs = new ArrayList<>();

    // propertyRef -> config instance (AdapterConfig, HandlerConfig, FlowStepConfig, etc)
    private Map<String, Object> configMap = new HashMap<>();

    // For support/debugging, property key-value map (merged from property sheets)
    private Map<String, Object> mergedProperties = new HashMap<>();

    private String businessFlowType;

    private String hipIntegrationType;
    private String businessFlowVersion;


    // --- Getters and Setters ---

    public String getHipIntegrationName() {
        return hipIntegrationName;
    }

    public void setHipIntegrationName(String hipIntegrationName) {
        this.hipIntegrationName = hipIntegrationName;
    }

    public String getServiceManagerName() {
        return serviceManagerName;
    }

    public void setServiceManagerName(String serviceManagerName) {
        this.serviceManagerName = serviceManagerName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public ThrottleSettings getThrottleSettings() {
        return throttleSettings;
    }

    public void setThrottleSettings(ThrottleSettings throttleSettings) {
        this.throttleSettings = throttleSettings;
    }


    public List<AdapterConfigRef> getAdapterConfigRefs() {
        return adapterConfigRefs;
    }

    public void setAdapterConfigRefs(List<AdapterConfigRef> adapterConfigRefs) {
        this.adapterConfigRefs = adapterConfigRefs;
    }

    public List<HandlerConfigRef> getHandlerConfigRefs() {
        return handlerConfigRefs;
    }

    public void setHandlerConfigRefs(List<HandlerConfigRef> handlerConfigRefs) {
        this.handlerConfigRefs = handlerConfigRefs;
    }

    public List<FlowStepConfigRef> getFlowStepConfigRefs() {
        return flowStepConfigRefs;
    }

    public void setFlowStepConfigRefs(List<FlowStepConfigRef> flowStepConfigRefs) {
        this.flowStepConfigRefs = flowStepConfigRefs;
    }

    public Map<String, Object> getConfigMap() {
        return configMap;
    }

    public void setConfigMap(Map<String, Object> configMap) {
        this.configMap = configMap;
    }

    public Map<String, Object> getMergedProperties() {
        return mergedProperties;
    }

    public void setMergedProperties(Map<String, Object> mergedProperties) {
        this.mergedProperties = mergedProperties;
    }

    public String getBusinessFlowType() {
        return businessFlowType;
    }

    public void setBusinessFlowVersion(String businessFlowVersion) {
        this.businessFlowVersion = businessFlowVersion;
    }

    public void setHipIntegrationType(String hipIntegrationType) {
        this.hipIntegrationType = hipIntegrationType;
    }

    public void setBusinessFlowType(String businessFlowType) {
        this.businessFlowType = businessFlowType;
    }

    public String getHipIntegrationType() {
        return hipIntegrationType;
    }

    public String getBusinessFlowVersion() {
        return businessFlowVersion;
    }

    // --- Utility for direct lookup ---
    @SuppressWarnings("unchecked")
    public <T> T getConfig(String propertyRef, Class<T> type) {
        Object obj = configMap.get(propertyRef);
        if (obj == null) return null;
        return (T) obj;
    }
}