package com.dell.it.hip.util.validation;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import io.xlate.edi.schema.EDISchemaException;
import io.xlate.edi.schema.Schema;
import io.xlate.edi.schema.SchemaFactory;
import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIStreamReader;

public class EdiValidator {

    // ————— X12 STRUCTURAL VALIDATION —————
    public static boolean structurallyValidateX12(String edi) {
        EDIInputFactory factory = EDIInputFactory.newFactory();
        factory.setProperty(EDIInputFactory.EDI_VALIDATE_CONTROL_STRUCTURE, true);

        try (InputStream is = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
             EDIStreamReader reader = factory.createEDIStreamReader(is)) {
            while (reader.hasNext()) {
                reader.next();  // envelope, group, transaction, segments, etc.
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // ————— EDIFACT STRUCTURAL VALIDATION —————
    public static boolean structurallyValidateEdifact(String edi) {
        // same logic works for EDIFACT interchange
        return structurallyValidateX12(edi);
    }

    // ————— X12 SCHEMA VALIDATION —————
	public static boolean validateAgainstSchemaX12(String edi, String schemaPath) {
		EDIInputFactory factory = EDIInputFactory.newFactory();
		try (InputStream schemaIn = new FileInputStream(schemaPath)) {
			SchemaFactory sf = SchemaFactory.newFactory();
			Schema schema = sf.createSchema(schemaIn);

			try (InputStream is = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
					EDIStreamReader reader = factory.createEDIStreamReader(is, schema)) {
				while (reader.hasNext()) {
					reader.next();
				}
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public static boolean validateAgainstSchemaEdifact(String ediContent, String schemaPath)
			throws FileNotFoundException, IOException, EDISchemaException {
		// Use a streaming reader so we never load the whole document into memory
		EDIInputFactory factory = EDIInputFactory.newFactory();
		try (InputStream schemaIn = new FileInputStream(schemaPath);
				InputStream ediIn = new ByteArrayInputStream(ediContent.getBytes(StandardCharsets.UTF_8))) {
			// Create a Schema object from the EDIFACT schema definition
			SchemaFactory sf = SchemaFactory.newFactory();
			Schema schema = sf.createSchema(schemaIn);
			try (EDIStreamReader reader = factory.createEDIStreamReader(ediIn, schema)) {
				{
					// Pull events; schema‐driven validation is applied as we read
					while (reader.hasNext()) {
						reader.next();
					}
					return true;
				}
			} catch (Exception e) {
				// on any validation error or IO problem, return false
				return false;
			}
		}
	}
}