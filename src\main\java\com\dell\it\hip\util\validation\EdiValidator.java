package com.dell.it.hip.util.validation;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import io.xlate.edi.schema.EDISchemaException;
import io.xlate.edi.schema.Schema;
import io.xlate.edi.schema.SchemaFactory;
import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIStreamReader;

public class EdiValidator {

    // ————— X12 STRUCTURAL VALIDATION —————
    public static boolean structurallyValidateX12(String edi) {
        EDIInputFactory factory = EDIInputFactory.newFactory();
        factory.setProperty(EDIInputFactory.EDI_VALIDATE_CONTROL_STRUCTURE, true);

        try (InputStream is = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
             EDIStreamReader reader = factory.createEDIStreamReader(is)) {
            while (reader.hasNext()) {
                reader.next();  // envelope, group, transaction, segments, etc.
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // ————— EDIFACT STRUCTURAL VALIDATION —————
    public static boolean structurallyValidateEdifact(String edi) {
        // same logic works for EDIFACT interchange
        return structurallyValidateX12(edi);
    }

    // ————— X12 SCHEMA VALIDATION —————
	public static boolean validateAgainstSchemaX12(String edi, String schemaContent) {
		try {
			EDIInputFactory factory = EDIInputFactory.newFactory();
			// use STAEDI to parse schemaContent string
			SchemaFactory sf = SchemaFactory.newFactory();
			Schema schema = sf.createSchema(new ByteArrayInputStream(schemaContent.getBytes(StandardCharsets.UTF_8)));
			try (InputStream is = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
				 EDIStreamReader reader = factory.createEDIStreamReader(is, schema)) {
				while (reader.hasNext()) reader.next();
				return true;
			}
		} catch (Exception e) {
			return false;
		}
	}

	public static boolean validateAgainstSchemaEdifact(String edi, String schemaContent) {
		try {
			EDIInputFactory factory = EDIInputFactory.newFactory();
			SchemaFactory sf = SchemaFactory.newFactory();
			Schema schema = sf.createSchema(new ByteArrayInputStream(schemaContent.getBytes(StandardCharsets.UTF_8)));
			try (InputStream is = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
				 EDIStreamReader reader = factory.createEDIStreamReader(is, schema)) {
				while (reader.hasNext()) {
					reader.next();
				}
				return true;
			}
		} catch (Exception e) {
			return false;
		}
	}
}