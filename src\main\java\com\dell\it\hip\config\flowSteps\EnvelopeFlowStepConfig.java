package com.dell.it.hip.config.flowSteps;

import java.util.Map;

import lombok.Data;

@Data
public class EnvelopeFlowStepConfig extends FlowStepConfig {
    private String envelopeHeader;    // e.g. "ISA*00*...~" or "<root>" or CSV header
    private String envelopeFooter;    // e.g. "IEA*1*000000001~" or "</root>" or null
    private String separator;         // e.g. "~" or "\n" (defaults per format if not set)
    private String dataFormat;        // Optionally override, else detected from payload
    private boolean validateEnvelope = true; // Run structural validation after enveloping
    private Map<String, String> dynamicHeaders; // Optional map for dynamic envelope values (placeholders)
}