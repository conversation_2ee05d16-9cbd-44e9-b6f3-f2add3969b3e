package com.dell.it.hip.strategy.flows;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.flowSteps.FlowRoutingConfig;
import com.dell.it.hip.config.flowSteps.FlowStepConfigRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
@Component("flowRouting")
public class FlowRoutingFlowStep implements FlowStepStrategy {

    @Autowired private RuleProcessor ruleProcessor;
    @Autowired private ServiceManager serviceManager;
    @Autowired private WiretapService wiretapService;
    @Autowired private TransactionLoggingUtil txLogger;

    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) {
        // Assume that stepConfigRef can provide us the real config (typed) for this step.
        FlowRoutingConfig config = (FlowRoutingConfig) def.getConfig(stepConfigRef.getPropertyRef(), FlowRoutingConfig.class);

        // Set documentType/flowIdentifier in header if present in config
        MessageBuilder<?> builder = MessageBuilder.fromMessage(message);
        if (config.getDocumentType() != null && !config.getDocumentType().isEmpty()) {
            builder.setHeader("HIP.documentType", config.getDocumentType());
        }
        if (config.getFlowIdentifier() != null && !config.getFlowIdentifier().isEmpty()) {
            builder.setHeader("HIP.flowIdentifier", config.getFlowIdentifier());
        }
        Message<?> inputMsg = builder.build();

        // Call RuleProcessor: returns message with HIP.targetFlowChannels header (List<String>)
        Message<?> result = ruleProcessor.processRules(def, config, inputMsg);

        // Extract channels to route to
        List<String> targetChannels = (List<String>) result.getHeaders().get("HIP.targetFlowChannels");

        if (targetChannels != null && !targetChannels.isEmpty()) {
            for (String chName : targetChannels) {
                MessageChannel channel = serviceManager.getChannels().get(chName.trim());
                if (channel != null) {
                    channel.send(result);
                    wiretapService.tap(result, def, stepConfigRef.getPropertyRef(), "info",
                            "Message routed to channel: " + chName + " via FlowIdentifierFlowStep");
                    txLogger.logRuleAction(def, stepConfigRef.getPropertyRef(), "FlowIdentifierFlowStep", "ROUTE", result);
                } else {
                    wiretapService.tap(result, def, stepConfigRef.getPropertyRef(), "error",
                            "Target channel not found: " + chName);
                    txLogger.logRuleAction(def, stepConfigRef.getPropertyRef(), "FlowIdentifierFlowStep", "CHANNEL_NOT_FOUND", result);
                }
            }
        } else {
            wiretapService.tap(result, def, stepConfigRef.getPropertyRef(), "warn",
                    "No target channels found in HIP.targetFlowChannels. Message not routed.");
            txLogger.logRuleAction(def, stepConfigRef.getPropertyRef(), "FlowIdentifierFlowStep", "NO_CHANNELS", result);
        }

        // Return an empty list (integration ends here for routed messages)
        return Collections.emptyList();
    }

    @Override
    public String getType() { return "FlowIdentifierFlowStep"; }
}
