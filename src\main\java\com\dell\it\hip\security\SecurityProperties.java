package com.dell.it.hip.security;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Security configuration properties loader
 */
@Component
@ConfigurationProperties(prefix = "app.security")
public class SecurityProperties {
    
    private String adminUsername;
    private String adminPassword;
    private String standardUsername;
    private String standardPassword;
    
    public String getAdminUsername() {
        return adminUsername;
    }
    
    public void setAdminUsername(String adminUsername) {
        this.adminUsername = adminUsername;
    }
    
    public String getAdminPassword() {
        return adminPassword;
    }
    
    public void setAdminPassword(String adminPassword) {
        this.adminPassword = adminPassword;
    }
    
    public String getStandardUsername() {
        return standardUsername;
    }
    
    public void setStandardUsername(String standardUsername) {
        this.standardUsername = standardUsername;
    }
    
    public String getStandardPassword() {
        return standardPassword;
    }
    
    public void setStandardPassword(String standardPassword) {
        this.standardPassword = standardPassword;
    }
}
