package com.dell.it.hip.config.FlowSteps;

import java.util.List;
import java.util.Objects;

/**
 * Configuration for AttributeProcessor flow step.
 */
public class AttributeProcessorConfig extends FlowStepConfig{
    private String propertyRef; // Unique key in property sheet/configMap
    private List<AttributeMapping> attributeMappings;

    public String getPropertyRef() {
        return propertyRef;
    }
    public void setPropertyRef(String propertyRef) {
        this.propertyRef = propertyRef;
    }

    public List<AttributeMapping> getAttributeMappings() {
        return attributeMappings;
    }
    public void setAttributeMappings(List<AttributeMapping> attributeMappings) {
        this.attributeMappings = attributeMappings;
    }

    @Override
    public String toString() {
        return "AttributeProcessorConfig{" +
                "propertyRef='" + propertyRef + '\'' +
                ", attributeMappings=" + attributeMappings +
                '}';
    }

    /**
     * How an attribute is derived.
     */
    public enum DerivedFromType {
        FILENAME,
        ROOT_ELEMENT,
        DERIVED_FROM_PAYLOAD
    }

    /**
     * Mapping for a single attribute extraction.
     */
    public static class AttributeMapping {
        private String attributeName;      // Name of header to add


        private DerivedFromType derivedFrom; // Enum to choose strategy
        private String expression;         // Regex, XPath, JSONPath, etc.
        private boolean required;          // If true, step will fail if missing
        private List<String> usedIn;
        public AttributeMapping() {}

        public AttributeMapping(String attributeName, DerivedFromType derivedFrom, String expression, boolean required) {
            this.attributeName = attributeName;
            this.derivedFrom = derivedFrom;
            this.expression = expression;
            this.required = required;
        }

        public String getAttributeName() {
            return attributeName;
        }
        public void setAttributeName(String attributeName) {
            this.attributeName = attributeName;
        }

        public DerivedFromType getDerivedFrom() {
            return derivedFrom;
        }
        public void setDerivedFrom(DerivedFromType derivedFrom) {
            this.derivedFrom = derivedFrom;
        }

        public String getExpression() {
            return expression;
        }
        public void setExpression(String expression) {
            this.expression = expression;
        }

        public boolean isRequired() {
            return required;
        }
        public void setRequired(boolean required) {
            this.required = required;
        }
        public List<String> getUsedIn() {
            return usedIn;
        }

        public void setUsedIn(List<String> usedIn) {
            this.usedIn = usedIn;
        }


        @Override
        public String toString() {
            return "AttributeMapping{" +
                    "attributeName='" + attributeName + '\'' +
                    ", derivedFrom=" + derivedFrom +
                    ", expression='" + expression + '\'' +
                    ", required=" + required +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof AttributeMapping)) return false;
            AttributeMapping that = (AttributeMapping) o;
            return required == that.required &&
                    Objects.equals(attributeName, that.attributeName) &&
                    derivedFrom == that.derivedFrom &&
                    Objects.equals(expression, that.expression);
        }

        @Override
        public int hashCode() {
            return Objects.hash(attributeName, derivedFrom, expression, required);
        }
    }
}