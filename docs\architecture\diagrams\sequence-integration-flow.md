# Sequence Diagram - Integration Flow

## Message Processing Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Gateway
    participant AUTH as Auth Service
    participant ORCH as Orchestrator
    participant RUNTIME as Runtime Service
    participant ADAPTER as Adapter
    participant FLOW as Flow Steps
    participant HANDLER as Handler
    participant DB as Database
    participant EXT as External System

    %% Authentication
    C->>+API: POST /api/integrations/{id}/process
    API->>+AUTH: Validate Token
    AUTH-->>-API: Token Valid
    
    %% Process Request
    API->>+ORCH: Process Message
    ORCH->>+DB: Get Integration Definition
    DB-->>-ORCH: Return Definition
    ORCH->>+RUNTIME: Execute Flow
    
    %% Message Processing
    RUNTIME->>+ADAPTER: Receive Message
    ADAPTER->>+EXT: Fetch Data
    EXT-->>-ADAPTER: Return Data
    ADAPTER-->>-RUNTIME: Message Received
    
    %% Flow Execution
    loop For Each Flow Step
        RUNTIME->>+FLOW: Execute Step
        FLOW->>FLOW: Process Message
        FLOW-->>-RUNTIME: Step Result
    end
    
    %% Handle Result
    alt Success
        RUNTIME->>+HANDLER: Handle Success
        HANDLER->>EXT: Send Response
        EXT-->>HANDLER: Acknowledge
        HANDLER-->>-RUNTIME: Success
    else Error
        RUNTIME->>+HANDLER: Handle Error
        HANDLER->>DB: Log Error
        HANDLER-->>-RUNTIME: Error Handled
    end
    
    %% Complete Processing
    RUNTIME-->>-ORCH: Flow Complete
    ORCH->>DB: Update Metrics
    ORCH-->>-API: Return Response
    API-->>-C: 200 OK
```

## Flow Registration Sequence

```mermaid
sequenceDiagram
    participant A as Admin
    participant API as API Gateway
    participant AUTH as Auth Service
    participant ORCH as Orchestrator
    participant VALID as Validator
    participant DB as Database
    participant CLUSTER as Cluster
    
    A->>+API: POST /api/integrations
    API->>+AUTH: Validate Token (Admin)
    AUTH-->>-API: Token Valid
    
    API->>+ORCH: Register Integration
    ORCH->>+VALID: Validate Definition
    VALID-->>-ORCH: Validation Result
    
    ORCH->>+DB: Save Definition
    DB-->>-ORCH: Save Confirmation
    
    ORCH->>+CLUSTER: Broadcast Update
    CLUSTER-->>-ORCH: Ack
    
    ORCH-->>-API: 201 Created
    API-->>-A: Success Response
```

## Error Handling Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Gateway
    participant RUNTIME as Runtime Service
    participant HANDLER as Error Handler
    participant DB as Database
    participant MON as Monitoring
    
    C->>+API: Process Request
    API->>+RUNTIME: Execute Flow
    
    RUNTIME->>RUNTIME: Process Message
    Note right of RUNTIME: Error Occurs
    
    RUNTIME->>+HANDLER: Handle Error
    HANDLER->>+DB: Log Error Details
    DB-->>-HANDLER: Log Confirmed
    
    HANDLER->>+MON: Record Metric
    MON-->>-HANDLER: Metric Recorded
    
    alt Recoverable
        HANDLER->>RUNTIME: Retry Processing
    else Fatal
        HANDLER->>RUNTIME: Abort Processing
    end
    
    HANDLER-->>-RUNTIME: Error Handled
    RUNTIME-->>-API: Error Response
    API-->>-C: 5xx Error
```

## Key Interactions Explained

### 1. Message Processing
1. Client sends a message to process through the API Gateway
2. Request is authenticated and forwarded to the Orchestrator
3. Runtime Service retrieves the integration definition
4. Adapter fetches data from the external system
5. Message flows through configured flow steps
6. Handler processes the final result or error

### 2. Flow Registration
1. Admin submits a new integration definition
2. Definition is validated for correctness
3. Definition is persisted to the database
4. Update is broadcast to the cluster
5. Success response is returned

### 3. Error Handling
1. When an error occurs during processing
2. Error is logged with details
3. Metrics are updated
4. Based on error type, either retry or abort
5. Appropriate error response is returned
