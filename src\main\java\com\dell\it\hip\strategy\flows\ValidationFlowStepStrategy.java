package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.ValidationFlowStepConfig;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.CsvSchemaValidator;
import com.dell.it.hip.util.validation.EdiValidator;
import com.dell.it.hip.util.validation.JsonSchemaValidator;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.dell.it.hip.util.validation.StructuralValidator;
import com.dell.it.hip.util.validation.XmlSchemaValidator;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("validation")
public class ValidationFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired
    private StringRedisTemplate redisTemplate;

  

    @Autowired
    private WiretapService wiretapService;

    @Override
    public String getType() {
        return "validation";
    }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        ValidationFlowStepConfig config = (ValidationFlowStepConfig) def.getConfigMap().get(stepRef.getPropertyRef());
        if (config == null) throw new IllegalStateException("No Validation config for: " + stepRef.getPropertyRef());

        String payload = String.valueOf(message.getPayload());
        String fmt = config.getInputFormat() != null ? config.getInputFormat() : MessageFormatDetector.detect(payload);

        // === 1. Structural validation ===
        boolean structuralValid = StructuralValidator.validate(payload, fmt);

        if (!structuralValid) {
            String err = "Structural validation failed for format: " + fmt;
            log.warn(err);
            wiretapService.tap(message, def, stepRef, "error", err);
            TransactionLoggingUtil.logError(message, def, stepRef,"ValidationFailure", err);

            if (config.isFailOnInvalid()) return Collections.emptyList();
            // else: continue and allow passthrough
        } else {
            wiretapService.tap(message, def, stepRef, "info", "Structural validation passed");
            TransactionLoggingUtil.logInfo(message, def, stepRef, "Structural validation passed");
        }

        // === 2. Schema validation (if enabled) ===
        if (config.isEnableSchemaValidation() && config.getSchemaKey() != null) {
            String schemaDoc = redisTemplate.opsForValue().get(config.getSchemaKey());
            if (schemaDoc == null) {
                String err = "Schema not found in Redis for key: " + config.getSchemaKey();
                log.error(err);
                wiretapService.tap(message, def, stepRef, "error", err);
                TransactionLoggingUtil.logError(message, def, stepRef,"ValidationFailure", err);
                if (config.isFailOnInvalid()) return Collections.emptyList();
            } else {
                boolean schemaValid = false;
                try {
                    switch (fmt) {
                        case "EDI_X12":
                            schemaValid = EdiValidator.validateAgainstSchemaX12(payload, config.getSchemaKey());
                            break;
                        case "EDI_EDIFACT":
                            schemaValid = EdiValidator.validateAgainstSchemaEdifact(payload, config.getSchemaKey());
                            break;
                        case "XML":
                            schemaValid = XmlSchemaValidator.validate(payload, config.getSchemaKey());
                            break;
                        case "JSON":
                            schemaValid = JsonSchemaValidator.validate(payload, config.getSchemaKey());
                            break;
                        case "CSV":
                            schemaValid = CsvSchemaValidator.validate(payload, config.getSchemaKey());
                            break;
                        default:
                            throw new IllegalArgumentException("Unsupported format for schema validation: " + fmt);
                    }
                } catch (Exception e) {
                    log.error("Exception during schema validation", e);
                    schemaValid = false;
                }

                if (!schemaValid) {
                    String err = "Schema validation failed for format: " + fmt + " with schema: " + config.getSchemaKey();
                    wiretapService.tap(message, def, stepRef, "error", err);
                    TransactionLoggingUtil.logError(message, def, stepRef,"ValidationFailure:Schema", err);
                    if (config.isFailOnInvalid()) return Collections.emptyList();
                } else {
                    wiretapService.tap(message, def, stepRef, "info", "Schema validation passed");
                    TransactionLoggingUtil.logInfo(message, def, stepRef, "Schema validation passed");
                }
            }
        }

        // === Success: pass message through ===
        return Collections.singletonList(message);
    }
}