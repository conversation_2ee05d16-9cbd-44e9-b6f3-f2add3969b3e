package com.dell.it.hip.core.registry;

import com.dell.it.hip.config.rules.RuleActionDescriptor;
import com.dell.it.hip.strategy.flows.rules.AddHeaderAction;
import com.dell.it.hip.strategy.flows.rules.RuleAction;
import com.dell.it.hip.strategy.flows.rules.StopRuleAction;
import org.springframework.stereotype.Component;

@Component
public class RuleActionRegistry {
    public RuleAction createAction(RuleActionDescriptor desc) {
        switch (desc.getType()) {
            case "AddHeader":
                String header = (String) desc.getParams().get("header");
                String value = (String) desc.getParams().get("value");
                return new AddHeaderAction();
            case "Stop":
                return new StopRuleAction();
            // Add more types as needed
            default:
                throw new IllegalArgumentException("Unknown RuleAction type: " + desc.getType());
        }
    }
}