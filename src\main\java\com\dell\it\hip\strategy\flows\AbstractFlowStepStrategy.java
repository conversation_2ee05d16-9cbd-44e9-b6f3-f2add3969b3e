package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

public abstract class AbstractFlowStepStrategy implements FlowStepStrategy {

    protected final Logger logger = LoggerFactory.getLogger(getClass());  

    @Autowired
    protected WiretapService wiretapService;
    @Override
    public List<Message<?>> executeStep(
            Message<?> message,
            FlowStepConfigRef ref,
            HIPIntegrationDefinition def
    ) throws Exception{
        try {
            List<Message<?>> result = doExecute(message, ref, def);

            // Info logging and wiretap (success)
            // Info logging and wiretap
           // transactionLoggingUtil.logInfo("FlowStep [" + getType() + "] executed successfully",def, ref, message);
            wiretapService.tap(
                    message, def, ref, "info",
                    "FlowStep executed: " + getType()
            );

            return result != null ? result : Collections.emptyList();

        } catch (Exception e) {
            logger.error("Error in flow step [{}]: {}", getType(), e.getMessage(), e);

            // Error logging and wiretap
        //    transactionLoggingUtil.logError(message,def,ref,"ERROR","FlowStep [" + getType() + "] execution failed: " +(e.getMessage() != null ? e.getMessage() : "Unknown error"));
            wiretapService.tap(
                    message, def, ref, "error",
                    "FlowStep error [" + getType() + "]: " + (e.getMessage() != null ? e.getMessage() : "Exception")
            );
            // Optionally, can return emptyList or send to error channel depending on system design
            return Collections.emptyList();
        }
    }

    /**
     * Main step execution logic for the concrete step strategy.
     * Return a list of messages (one or many).
     */
    protected abstract List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef ref,
            HIPIntegrationDefinition def
    ) throws Exception;

    @Override
    public abstract String getType();
}